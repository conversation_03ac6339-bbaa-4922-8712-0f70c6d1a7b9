import { useState, useRef } from "react";
import { checkTimeSlotCapacity } from "@/lib/actions/database";
import { WORKING_HOURS, TimeSlotCapacityMap } from "./appointment-utils";

export function useAppointmentCapacity() {
  const [timeSlotCapacity, setTimeSlotCapacity] = useState<TimeSlotCapacityMap>(
    {}
  );
  const [loadingTimeSlots, setLoadingTimeSlots] = useState(false);

  // Cache to prevent redundant API calls
  const capacityCache = useRef<Record<string, TimeSlotCapacityMap>>({});

  const fetchTimeSlotCapacities = async (date: string) => {
    if (!date) return;

    // Check cache first
    if (capacityCache.current[date]) {
      setTimeSlotCapacity(capacityCache.current[date]);
      return;
    }

    setLoadingTimeSlots(true);
    const capacities: TimeSlotCapacityMap = {};

    try {
      // Fetch all capacities in parallel for better performance
      const capacityPromises = WORKING_HOURS.map(async (time) => {
        try {
          const capacity = await checkTimeSlotCapacity(date, time);
          return { time, capacity };
        } catch (error) {
          console.error(`Error checking capacity for ${time}:`, error);
          return {
            time,
            capacity: {
              currentCount: 0,
              maxCapacity: 3,
              available: true,
            },
          };
        }
      });

      const results = await Promise.all(capacityPromises);

      // Build capacities map from results
      results.forEach(({ time, capacity }) => {
        capacities[time] = capacity;
      });

      // Cache the results
      capacityCache.current[date] = capacities;
      setTimeSlotCapacity(capacities);
    } finally {
      setLoadingTimeSlots(false);
    }
  };

  return {
    timeSlotCapacity,
    loadingTimeSlots,
    fetchTimeSlotCapacities,
  };
}
