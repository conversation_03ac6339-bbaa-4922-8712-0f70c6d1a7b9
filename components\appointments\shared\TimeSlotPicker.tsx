"use client";

import { memo } from "react";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  WORKING_HOURS,
  isTimeSlotInPast,
  TimeSlotCapacityMap,
} from "./appointment-utils";

interface TimeSlotPickerProps {
  selectedDate: string;
  selectedTime: string;
  onTimeSelect: (time: string) => void;
  timeSlotCapacity: TimeSlotCapacityMap;
  loadingTimeSlots: boolean;
  originalTime?: string; // For edit mode - to highlight original time
  originalDate?: string; // For edit mode - to allow past times for original slot
  isEditMode?: boolean;
}

// Define the capacity interface
interface TimeSlotCapacity {
  currentCount: number;
  maxCapacity: number;
  available: boolean;
}

// Memoized TimeSlotButton component for better performance
const TimeSlotButton = memo(
  ({
    time,
    isSelected,
    isFull,
    isNearFull,
    isPastTime,
    isOriginal,
    isButtonDisabled,
    capacity,
    onTimeSelect,
  }: {
    time: string;
    isSelected: boolean;
    isFull: boolean;
    isNearFull: boolean;
    isPastTime: boolean;
    isOriginal: boolean;
    isButtonDisabled: boolean;
    capacity: TimeSlotCapacity | undefined;
    onTimeSelect: (time: string) => void;
  }) => {
    return (
      <button
        key={time}
        disabled={isButtonDisabled}
        className={cn(
          "p-4 border-2 rounded-xl text-center transition-all duration-200 font-medium relative",
          // If button is disabled (including original time)
          isButtonDisabled
            ? isOriginal
              ? "border-blue-300 dark:border-blue-700 bg-blue-100/50 dark:bg-blue-950/30 cursor-not-allowed opacity-70 hover:border-blue-300 hover:bg-blue-100/50"
              : "border-muted bg-muted/50 cursor-not-allowed opacity-60 hover:border-muted hover:bg-muted/50"
            : "hover:shadow-md",
          // Selected state (only if not disabled)
          isSelected && !isButtonDisabled
            ? "border-primary bg-primary text-white shadow-lg"
            : !isButtonDisabled &&
                "border-border hover:border-primary/50 bg-card",
          // Near full indicator (only if not disabled and not original)
          isNearFull &&
            !isButtonDisabled &&
            !isSelected &&
            !isOriginal &&
            "ring-2 ring-amber-200 dark:ring-amber-800/50 border-amber-400 dark:border-amber-600"
        )}
        onClick={() => !isButtonDisabled && onTimeSelect(time)}
      >
        {isFull && !isOriginal && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-destructive rounded-full"></div>
        )}
        {isPastTime && !isOriginal && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-muted-foreground rounded-full"></div>
        )}
        {isNearFull && !isButtonDisabled && !isSelected && !isOriginal && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-amber-500 rounded-full"></div>
        )}
        {isOriginal && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
        )}

        <div className="text-lg">{time}</div>
        {capacity && !isPastTime && !isOriginal && (
          <div className="text-xs mt-1 opacity-70">
            {isFull
              ? "Dolu"
              : `${capacity.currentCount}/${capacity.maxCapacity}`}
          </div>
        )}
        {isPastTime && !isOriginal && (
          <div className="text-xs mt-1 opacity-70">Geçti</div>
        )}
        {isOriginal && (
          <div className="text-xs mt-1 opacity-70 font-medium text-blue-600 dark:text-blue-400">
            Seçilemez
          </div>
        )}
      </button>
    );
  }
);

TimeSlotButton.displayName = "TimeSlotButton";

export default function TimeSlotPicker({
  selectedDate,
  selectedTime,
  onTimeSelect,
  timeSlotCapacity,
  loadingTimeSlots,
  originalTime,
  originalDate,
  isEditMode = false,
}: TimeSlotPickerProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <div className="w-2 h-2 bg-primary rounded-full"></div>
        <h3 className="text-lg font-semibold text-foreground">Saat Seçin</h3>
      </div>

      <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400 mb-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-sm font-medium">Kapasite Bilgisi</span>
        </div>
        <div className="text-sm text-blue-700 dark:text-blue-400 grid grid-cols-2 md:grid-cols-5 gap-3">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
            <span>Uygun (0-1/3)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
            <span>Az yer kaldı (2/3)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-red-500 rounded-full"></div>
            <span>Dolu (3/3)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
            <span>Geçmiş saat</span>
          </div>
          {isEditMode && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Mevcut randevu (seçilemez)</span>
            </div>
          )}
        </div>
      </div>

      {/* Time Slots - Loading State */}
      <div className="relative">
        {loadingTimeSlots && (
          <div className="absolute inset-0 bg-card/80 backdrop-blur-sm z-10 rounded-lg flex items-center justify-center">
            <div className="text-center space-y-3">
              <Loader2 className="w-6 h-6 animate-spin mx-auto text-primary" />
              <div className="text-sm text-muted-foreground">
                Saat bilgileri yükleniyor...
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
          {WORKING_HOURS.map((time) => {
            const isSelected = selectedTime === time;
            const capacity = timeSlotCapacity[time];
            const isFull = capacity && !capacity.available;
            const isNearFull = capacity && capacity.currentCount >= 2;
            const isPastTime = isTimeSlotInPast(selectedDate, time);

            // More robust original time comparison - time should already be normalized from props
            const isOriginal =
              isEditMode &&
              time === originalTime &&
              selectedDate === originalDate;

            // In edit mode, disable the original slot and past times (except original)
            // In new appointment mode, disable full and past times
            const isDisabled = isEditMode
              ? isOriginal || (isPastTime && !isOriginal) || isFull
              : isFull || isPastTime;

            const isButtonDisabled = isDisabled || loadingTimeSlots;

            return (
              <TimeSlotButton
                key={time}
                time={time}
                isSelected={isSelected}
                isFull={isFull}
                isNearFull={isNearFull}
                isPastTime={isPastTime}
                isOriginal={isOriginal}
                isButtonDisabled={isButtonDisabled}
                capacity={capacity}
                onTimeSelect={onTimeSelect}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}
