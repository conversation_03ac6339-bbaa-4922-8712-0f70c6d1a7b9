import { Card, CardContent } from "@/components/ui/card";
import { Heart, Award, Clock, Target } from "lucide-react";

const features = [
  { title: "Ka<PERSON>ın Do<PERSON>u <PERSON>", description: "<PERSON><PERSON><PERSON><PERSON> ve destekleyici", Icon: Heart, gradient: "from-pink-500 to-rose-500" },
  { title: "<PERSON>zman Kadın An<PERSON>örle<PERSON>", description: "<PERSON><PERSON><PERSON><PERSON> ekip", Icon: Award, gradient: "from-purple-500 to-pink-500" },
  { title: "Esnek Saatler", description: "<PERSON><PERSON><PERSON> tempoya uygun", Icon: Clock, gradient: "from-rose-500 to-pink-500" },
  { title: "Kişiselleştirilmiş Plan", description: "<PERSON><PERSON><PERSON> odaklı ya<PERSON>şı<PERSON>", Icon: Target, gradient: "from-pink-600 to-rose-600" },
];

export default function WhyUs() {
  return (
    <section id="neden" className="section-padding bg-gradient-to-br from-pink-50/50 to-purple-50/50 dark:from-pink-950/20 dark:to-purple-950/20">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">Neden Loca Fit?</h2>
          <p className="text-muted-foreground mt-2">Binlerce kadının tercihi</p>
        </div>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map(({ title, description, Icon, gradient }) => (
            <Card key={title} className="glass-card-modern hover-lift group">
              <CardContent className="pt-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r ${gradient} text-white mb-4 group-hover:scale-110 transition-transform`}>
                  <Icon className="size-6" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{title}</h3>
                <p className="text-sm text-muted-foreground">{description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

