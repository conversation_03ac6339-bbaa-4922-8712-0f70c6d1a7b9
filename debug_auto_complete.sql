-- Debug script for auto_complete_past_sessions function
-- <PERSON><PERSON> script'i Supabase SQL Editor'da çalıştırarak fonksiyonu test edebilirsiniz

-- 1. <PERSON>nce mevcut scheduled randevuları kontrol edelim
SELECT
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as combined_datetime,
  NOW() as current_time,
  CURRENT_DATE as current_date,
  CASE
    WHEN session_date < CURRENT_DATE THEN 'PAST DATE - SHOULD BE COMPLETED'
    WHEN session_date = CURRENT_DATE AND (session_date::TIMESTAMP + session_time::INTERVAL) < NOW()
    THEN 'TODAY BUT PAST TIME - SHOULD BE COMPLETED'
    ELSE 'FUTURE APPOINTMENT'
  END as should_update
FROM flex_package_sessions
WHERE status = 'scheduled'
ORDER BY session_date, session_time;

-- 2. <PERSON>on<PERSON><PERSON><PERSON><PERSON> çalıştır
SELECT auto_complete_past_sessions() as updated_count;

-- 3. <PERSON><PERSON><PERSON> kontrol et
SELECT 
  id,
  session_date,
  session_time,
  status,
  updated_at,
  (session_date::TIMESTAMP + session_time::INTERVAL) as combined_datetime
FROM flex_package_sessions 
WHERE session_date <= CURRENT_DATE
ORDER BY session_date DESC, session_time DESC
LIMIT 10;

-- 4. Eğer hala sorun varsa, manuel olarak test edelim
-- Bu sorgu hangi randevuların güncellenmesi gerektiğini gösterir:
SELECT 
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as appointment_time,
  NOW() as current_time,
  (session_date::TIMESTAMP + session_time::INTERVAL) < NOW() as is_past
FROM flex_package_sessions 
WHERE status = 'scheduled'
  AND session_date <= CURRENT_DATE;

-- 5. Özel test: 10 Eylül 2025 12:00 randevusu için
SELECT
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as appointment_time,
  NOW() as current_time,
  session_date < CURRENT_DATE as is_past_date,
  (session_date = CURRENT_DATE AND (session_date::TIMESTAMP + session_time::INTERVAL) < NOW()) as is_today_but_past_time
FROM flex_package_sessions
WHERE session_date = '2025-09-10'
  AND session_time = '12:00:00'
  AND status = 'scheduled';

-- 6. Manuel güncelleme (eğer fonksiyon çalışmazsa):
/*
UPDATE flex_package_sessions
SET status = 'completed',
    updated_at = NOW()
WHERE status = 'scheduled'
  AND (
    session_date < CURRENT_DATE
    OR
    (session_date = CURRENT_DATE AND
     (session_date::TIMESTAMP + session_time::INTERVAL) < NOW())
  );
*/
