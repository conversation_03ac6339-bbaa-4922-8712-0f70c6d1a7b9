-- Debug script for auto_complete_past_sessions function
-- Bu script'i Supabase SQL Editor'da çalıştırarak fonksiyonu test edebilirsiniz

-- 1. <PERSON>nce mevcut scheduled randevuları kontrol edelim (Türkiye saati ile)
SELECT
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as appointment_time,
  NOW() as current_time_utc,
  (NOW() + INTERVAL '3 hours') as current_time_tr,
  CURRENT_DATE as current_date_utc,
  (NOW() + INTERVAL '3 hours')::DATE as current_date_tr,
  CASE
    WHEN session_date < (NOW() + INTERVAL '3 hours')::DATE THEN 'PAST DATE - SHOULD BE COMPLETED'
    WHEN session_date = (NOW() + INTERVAL '3 hours')::DATE
         AND (session_date::TIMESTAMP + session_time::INTERVAL) < (NOW() + INTERVAL '3 hours')
    THEN 'TODAY BUT PAST TIME - SHOULD BE COMPLETED'
    ELSE 'FUTURE APPOINTMENT'
  END as should_update
FROM flex_package_sessions
WHERE status = 'scheduled'
ORDER BY session_date, session_time;

-- 2. Fonksiyonu çalıştır
SELECT auto_complete_past_sessions() as updated_count;

-- 3. Sonucu kontrol et
SELECT 
  id,
  session_date,
  session_time,
  status,
  updated_at,
  (session_date::TIMESTAMP + session_time::INTERVAL) as combined_datetime
FROM flex_package_sessions 
WHERE session_date <= CURRENT_DATE
ORDER BY session_date DESC, session_time DESC
LIMIT 10;

-- 4. Eğer hala sorun varsa, manuel olarak test edelim
-- Bu sorgu hangi randevuların güncellenmesi gerektiğini gösterir:
SELECT 
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as appointment_time,
  NOW() as current_time,
  (session_date::TIMESTAMP + session_time::INTERVAL) < NOW() as is_past
FROM flex_package_sessions 
WHERE status = 'scheduled'
  AND session_date <= CURRENT_DATE;

-- 5. Özel test: 10 Eylül 2025 12:00 randevusu için (Türkiye saati ile)
SELECT
  id,
  session_date,
  session_time,
  status,
  (session_date::TIMESTAMP + session_time::INTERVAL) as appointment_time,
  NOW() as current_time_utc,
  (NOW() + INTERVAL '3 hours') as current_time_tr,
  (NOW() + INTERVAL '3 hours')::DATE as current_date_tr,
  session_date < (NOW() + INTERVAL '3 hours')::DATE as is_past_date,
  (session_date = (NOW() + INTERVAL '3 hours')::DATE
   AND (session_date::TIMESTAMP + session_time::INTERVAL) < (NOW() + INTERVAL '3 hours')) as is_today_but_past_time,
  CASE
    WHEN session_date < (NOW() + INTERVAL '3 hours')::DATE THEN 'SHOULD BE COMPLETED - PAST DATE'
    WHEN session_date = (NOW() + INTERVAL '3 hours')::DATE
         AND (session_date::TIMESTAMP + session_time::INTERVAL) < (NOW() + INTERVAL '3 hours')
    THEN 'SHOULD BE COMPLETED - TODAY BUT PAST TIME'
    ELSE 'FUTURE - NO UPDATE NEEDED'
  END as update_status
FROM flex_package_sessions
WHERE session_date = '2025-09-10'
  AND session_time = '12:00:00';

-- 6. Manuel güncelleme (eğer fonksiyon çalışmazsa) - Türkiye saati ile:
/*
UPDATE flex_package_sessions
SET status = 'completed',
    updated_at = NOW()
WHERE status = 'scheduled'
  AND (
    session_date < (NOW() + INTERVAL '3 hours')::DATE
    OR
    (session_date = (NOW() + INTERVAL '3 hours')::DATE AND
     (session_date::TIMESTAMP + session_time::INTERVAL) < (NOW() + INTERVAL '3 hours'))
  );
*/

-- 7. Sadece 10 Eylül 12:00 randevusunu manuel güncelleme:
/*
UPDATE flex_package_sessions
SET status = 'completed',
    updated_at = NOW()
WHERE session_date = '2025-09-10'
  AND session_time = '12:00:00'
  AND status = 'scheduled';
*/
