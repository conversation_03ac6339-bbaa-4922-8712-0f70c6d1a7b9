CREATE TABLE day_based_packages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  name VARCHAR NOT NULL, -- "7 Günlük Başlangıç", "30 Günlük Premium", "45 Günlük Esnek"
  description TEXT,
  validity_days INTEGER NOT NULL, -- 7, 15, 30, 45, 60
  session_count INTEGER NOT NULL, -- Toplam seans sayısı
  price DECIMAL(10,2) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  color_code VARCHAR(7) -- UI için renk kodu #FF5733
);