/**
 * <PERSON><PERSON><PERSON><PERSON> silinip silinemeyeceğini kontrol et (12 saat kuralı)
 */
export function canDeleteSession(
  sessionDate: string,
  sessionTime: string
): boolean {
  const sessionDateTime = new Date(`${sessionDate}T${sessionTime}`);
  const now = new Date();
  const timeDifference = sessionDateTime.getTime() - now.getTime();
  const hoursDifference = timeDifference / (1000 * 60 * 60);

  return hoursDifference > 12;
}
