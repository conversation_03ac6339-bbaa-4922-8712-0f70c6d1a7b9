import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { LotusIcon } from "@/components/ui/lotus-icon";

type Props = {
  phone: string;
};

export default function NavBar({ phone }: Props) {
  const sections = [
    { href: "#programlar", label: "Programlar" },
    { href: "#neden", label: "Neden Loca Fit" },
    { href: "#fiyat", label: "Üyelik" },
    { href: "#yorumlar", label: "Yorumlar" },
    { href: "#sss", label: "SSS" },
    { href: "#iletisim", label: "İletişim" },
  ];

  return (
    <header className="sticky top-0 z-50 backdrop-blur-xl border-b bg-white/70 dark:bg-black/30">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 h-16 flex items-center justify-between">
        <Link href="/" className="flex items-center gap-2" aria-label="Loca Fit Studio ana sayfa">
          <LotusIcon size={28} />
          <span className="font-extrabold tracking-tight text-gradient-modern">Loca Fit Studio</span>
        </Link>

        <nav className="hidden md:flex items-center gap-6 text-sm" aria-label="Birincil">
          {sections.map((s) => (
            <Link key={s.href} href={s.href} className="hover:opacity-80">
              {s.label}
            </Link>
          ))}
        </nav>

        <div className="flex items-center gap-2">
          <ThemeToggle />
          <Button asChild size="lg" variant="gradient" className="hidden md:inline-flex">
            <Link href="/appointments/new">Ücretsiz Deneme</Link>
          </Button>
          <Button asChild size="icon" variant="glass" aria-label="Telefon ile ara">
            <a href={`tel:${phone}`}>📞</a>
          </Button>
        </div>
      </div>
    </header>
  );
}

