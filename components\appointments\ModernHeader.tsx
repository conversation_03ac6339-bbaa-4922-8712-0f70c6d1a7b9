"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ThemeSwitcher } from "@/components/theme-switcher";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  Du<PERSON>bell,
  User,
  LogOut,
  Target,
  Calendar,
  CheckCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { Member } from "@/lib/supabase/types";

interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
  activePackages: number;
}

interface ModernHeaderProps {
  memberInfo: Member | null;
  stats: FlexDashboardStats;
  onLogout: () => void;
}

// Stats Item Component
function StatsItem({
  icon: Icon,
  value,
  label,
}: {
  icon: React.ComponentType<{ className?: string }>;
  value: number;
  label: string;
}) {
  return (
    <div className="flex items-center gap-2 px-3 py-1.5 bg-secondary/50 rounded-lg">
      <Icon className="w-4 h-4 text-muted-foreground" />
      <div>
        <span className="text-lg font-bold text-foreground">{value}</span>
        <span className="text-xs text-muted-foreground block">{label}</span>
      </div>
    </div>
  );
}

export default function ModernHeader({
  memberInfo,
  stats,
  onLogout,
}: ModernHeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.header
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-500 backdrop-blur-xl",
        isScrolled
          ? "bg-background/80 border-b border-border/50 py-2"
          : "bg-transparent py-4"
      )}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6">
        <div className="flex items-center justify-between">
          {/* Brand */}
          <Link href="/" className="flex items-center gap-2 group">
            <div className="w-8 h-8 bg-gradient-brand rounded-lg flex items-center justify-center">
              <Dumbbell className="w-5 h-5 text-white" />
            </div>
            <span className="text-lg font-semibold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent hidden sm:block">
              Loca Fit Studio
            </span>
          </Link>

          {/* Stats Bar - Center */}
          <div
            className={cn(
              "hidden md:flex items-center gap-2 transition-all duration-500",
              isScrolled ? "opacity-100 scale-100" : "opacity-0 scale-95"
            )}
          >
            <StatsItem
              icon={Target}
              value={stats.remainingSessions}
              label="Kalan"
            />
            <StatsItem
              icon={Calendar}
              value={stats.scheduledSessions}
              label="Planlanmış"
            />
            <StatsItem
              icon={CheckCircle}
              value={stats.completedSessions}
              label="Tamamlanan"
            />
          </div>

          {/* Profile */}
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="hidden sm:flex items-center gap-2 px-2.5 py-1.5 bg-secondary/50 backdrop-blur-sm rounded-full">
              <div className="w-6 h-6 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full flex items-center justify-center">
                <User className="w-3 h-3 text-white" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {memberInfo?.first_name || "Üye"}
              </span>
            </div>
            <ThemeSwitcher />
            <Button
              variant="outline"
              size="sm"
              onClick={onLogout}
              className="hover:bg-destructive/10 hover:text-destructive border-border/50"
            >
              <LogOut className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </motion.header>
  );
}
