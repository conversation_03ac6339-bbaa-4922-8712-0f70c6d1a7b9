"use client";

import { useEffect } from 'react';
import { useAppointmentsStore } from '@/stores/appointments-store';
import type { MinimalMember } from '@/lib/actions/database';

interface AppointmentsProviderProps {
  children: React.ReactNode;
  memberPhone: string;
  memberInfo: MinimalMember;
}

export default function AppointmentsProvider({
  children,
  memberPhone,
  memberInfo,
}: AppointmentsProviderProps) {
  const initializeFromCookie = useAppointmentsStore((state) => state.initializeFromCookie);

  useEffect(() => {
    // Initialize store with server-side data
    initializeFromCookie(memberPhone, memberInfo);
  }, [memberPhone, memberInfo, initializeFromCookie]);

  return <>{children}</>;
}
