"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import Link from "next/link";
import {
  Package,
  Plus,
  ChevronDown,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import SessionItem from "./SessionItem";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

interface PackagesSectionProps {
  flexPackages: FlexPackageGroup[];
  expandedPackages: Record<string, boolean>;
  togglePackageExpansion: (packageId: string) => void;
  handleDeleteSession: (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => void;
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const calculateRemainingDays = (expiryDate: string): number => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

const getExpiryColor = (status: string, remainingDays: number) => {
  if (status === "completed") return "text-emerald-600 dark:text-emerald-400";
  if (remainingDays <= 7) return "text-destructive";
  if (remainingDays <= 30) return "text-amber-600 dark:text-amber-400";
  return "text-emerald-600 dark:text-emerald-400";
};

export default function PackagesSection({
  flexPackages,
  expandedPackages,
  togglePackageExpansion,
  handleDeleteSession,
}: PackagesSectionProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-foreground">Paketlerim</h2>
        <Link href="/appointments/new">
          <Button size="sm" variant="outline" className="h-8 px-3">
            <Plus className="w-4 h-4 mr-1" />
            Randevu Al
          </Button>
        </Link>
      </div>

      {flexPackages.length === 0 ? (
        <div className="rounded-lg border border-dashed border-border/60 p-8 text-center">
          <Package className="w-8 h-8 mx-auto text-muted-foreground/60 mb-3" />
          <h3 className="font-medium text-foreground mb-1">Aktif paket yok</h3>
        </div>
      ) : (
        <div className="space-y-3">
          {flexPackages.map((flexPackage, index) => {
            const pkg = flexPackage.memberPackage;
            const pkgStatus = pkg.status;
            const isExpanded = expandedPackages[pkg.id] ?? false;

            const total = flexPackage.totalSessions;
            const used = flexPackage.usedSessions;
            const remaining = flexPackage.remainingSessions;
            const progressPct =
              total > 0 ? Math.round((used / total) * 100) : 0;

            const scheduledSessions = flexPackage.sessions.filter(
              (s) => s.status === "scheduled"
            );

            const remainingDays = calculateRemainingDays(
              flexPackage.expiryDate
            );

            const isExpiring = remainingDays <= 7;
            const isCompleted = pkgStatus === "completed";

            return (
              <motion.div
                key={flexPackage.memberPackage.id}
                variants={fadeInUp}
                transition={{ delay: index * 0.05 }}
                className="group"
              >
                <Collapsible
                  open={isExpanded}
                  onOpenChange={() =>
                    togglePackageExpansion(flexPackage.memberPackage.id)
                  }
                  className="rounded-lg border border-border/50 bg-card/50 hover:bg-card transition-colors"
                >
                  <CollapsibleTrigger className="w-full p-3 text-left">
                    <div className="flex items-center justify-between gap-3">
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div
                          className={cn(
                            "w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium",
                            isCompleted
                              ? "bg-emerald-500"
                              : isExpiring
                              ? "bg-amber-500"
                              : "bg-primary"
                          )}
                        >
                          {isCompleted ? (
                            <CheckCircle className="w-4 h-4" />
                          ) : (
                            <Package className="w-4 h-4" />
                          )}
                        </div>

                        <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-foreground truncate">
                              {flexPackage.packageName}
                            </h3>
                            {isCompleted && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400"
                              >
                                Tamamlandı
                              </Badge>
                            )}
                            {isExpiring && !isCompleted && (
                              <Badge
                                variant="secondary"
                                className="text-xs bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
                              >
                                <AlertCircle className="w-3 h-3 mr-1" />
                                {remainingDays}g
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>
                              {remaining}/{total} seans
                            </span>
                            <span>•</span>
                            <span>{scheduledSessions.length} planlanmış</span>
                            {!isCompleted && (
                              <>
                                <span>•</span>
                                <span
                                  className={cn(
                                    isExpiring
                                      ? "text-amber-600 dark:text-amber-400"
                                      : "text-muted-foreground"
                                  )}
                                >
                                  {remainingDays} gün
                                </span>
                              </>
                            )}
                          </div>

                          <div className="w-full h-1.5 bg-secondary rounded-full mt-2 overflow-hidden">
                            <div
                              className={cn(
                                "h-full rounded-full transition-all duration-300",
                                isCompleted
                                  ? "bg-emerald-500"
                                  : isExpiring
                                  ? "bg-amber-500"
                                  : "bg-primary"
                              )}
                              style={{ width: `${progressPct}%` }}
                            />
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {scheduledSessions.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {scheduledSessions.length}
                          </Badge>
                        )}
                        <ChevronDown
                          className={cn(
                            "w-4 h-4 text-muted-foreground transition-transform duration-200",
                            isExpanded && "rotate-180"
                          )}
                        />
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="border-t border-border/30">
                    <div className="p-3">
                      {scheduledSessions.length === 0 ? (
                        <div className="text-center py-4">
                          <p className="text-sm text-muted-foreground mb-3">
                            {isCompleted
                              ? "Bu paket tamamlanmıştır"
                              : "Planlanmış randevu yok"}
                          </p>
                          {!isCompleted && (
                            <Link href="/appointments/new">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8"
                              >
                                <Plus className="w-4 h-4 mr-1" />
                                Randevu Al
                              </Button>
                            </Link>
                          )}
                        </div>
                      ) : (
                        <div className="space-y-2">
                          {scheduledSessions
                            .sort(
                              (a, b) =>
                                new Date(a.session_date).getTime() -
                                new Date(b.session_date).getTime()
                            )
                            .map((session, sessionIndex) => (
                              <SessionItem
                                key={session.id}
                                session={session}
                                packageName={flexPackage.packageName}
                                onDelete={() =>
                                  handleDeleteSession(
                                    session.id,
                                    session.session_date,
                                    session.session_time
                                  )
                                }
                                canDelete={canDeleteSession(
                                  session.session_date,
                                  session.session_time
                                )}
                                index={sessionIndex}
                              />
                            ))}
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.section>
  );
}
