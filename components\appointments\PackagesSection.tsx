"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import Link from "next/link";
import {
  Package,
  Plus,
  Calendar,
  ChevronDown,
  Target,
  TrendingUp,
  Clock,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import SessionItem from "./SessionItem";
import type {
  Member,
  MemberFlexPackageWithDetails,
} from "@/lib/supabase/types";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface FlexPackageGroup {
  packageName: string;
  sessions: ServerFlexPackageSessionWithDetails[];
  memberPackage: MemberFlexPackageWithDetails;
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  expiryDate: string;
}

interface PackagesSectionProps {
  flexPackages: FlexPackageGroup[];
  expandedPackages: Record<string, boolean>;
  togglePackageExpansion: (packageId: string) => void;
  handleDeleteSession: (
    sessionId: string,
    sessionDate: string,
    sessionTime: string
  ) => void;
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

const formatDate = (dateString: string) =>
  new Date(dateString).toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    year: "numeric",
  });

const calculateRemainingDays = (expiryDate: string): number => {
  const today = new Date();
  const expiry = new Date(expiryDate);
  const diffTime = expiry.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

const getExpiryColor = (status: string, remainingDays: number) => {
  if (status === "completed") return "text-emerald-600 dark:text-emerald-400";
  if (remainingDays <= 7) return "text-destructive";
  if (remainingDays <= 30) return "text-amber-600 dark:text-amber-400";
  return "text-emerald-600 dark:text-emerald-400";
};

export default function PackagesSection({
  flexPackages,
  expandedPackages,
  togglePackageExpansion,
  handleDeleteSession,
}: PackagesSectionProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-foreground">Aktif Paketler</h2>
          <p className="text-muted-foreground">
            Randevularınızı ve paket durumunuzu takip edin
          </p>
        </div>
      </div>

      {flexPackages.length === 0 ? (
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-card to-secondary/10 border border-border/30 p-10 backdrop-blur-sm">
          <div className="text-center space-y-6">
            <div className="w-24 h-24 mx-auto bg-gradient-to-br from-primary/20 to-primary/10 rounded-2xl flex items-center justify-center">
              <Package className="w-12 h-12 text-primary" />
            </div>
            <div className="space-y-3">
              <h3 className="text-2xl font-bold text-foreground">
                Henüz Aktif Paketiniz Yok
              </h3>
              <p className="text-muted-foreground text-lg max-w-md mx-auto">
                Fitness yolculuğunuza başlamak için bir esnek paket seçin ve
                randevularınızı planlayın.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
              <Button
                size="lg"
                className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 px-8 py-6 text-lg rounded-xl"
                asChild
              >
                <Link href="/appointments/new">
                  <Plus className="w-5 h-5 mr-2" />
                  Yeni Randevu Al
                </Link>
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div className="grid gap-6">
          {flexPackages.map((flexPackage, index) => {
            const pkg = flexPackage.memberPackage;
            const pkgStatus = pkg.status;
            const isExpanded = expandedPackages[pkg.id] ?? true;

            const total = flexPackage.totalSessions;
            const used = flexPackage.usedSessions;
            const progressPct =
              pkgStatus === "completed"
                ? 100
                : Math.min(100, Math.round((used / total) * 100));

            const scheduledSessions = flexPackage.sessions.filter(
              (s) => s.status === "scheduled"
            );

            const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
            const completedThisWeek = flexPackage.sessions.filter(
              (s) =>
                s.status === "completed" &&
                new Date(s.session_date) >= sevenDaysAgo
            ).length;

            const remainingDays = calculateRemainingDays(
              flexPackage.expiryDate
            );
            const expiryColor = getExpiryColor(pkgStatus, remainingDays);
            const statusLabel =
              pkgStatus === "completed" ? "Tamamlandı" : "Aktif";

            return (
              <motion.div
                key={flexPackage.memberPackage.id}
                variants={fadeInUp}
                transition={{ delay: index * 0.1 }}
                className="group"
              >
                <Collapsible
                  open={isExpanded}
                  onOpenChange={() =>
                    togglePackageExpansion(flexPackage.memberPackage.id)
                  }
                  className="overflow-hidden rounded-2xl bg-gradient-to-br from-card to-secondary/5 border border-border/30 shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-sm"
                >
                  <CollapsibleTrigger className="w-full p-4 hover:bg-secondary/10 transition-colors">
                    <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
                      <div className="flex items-center gap-5">
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary via-primary/80 to-primary/60 rounded-xl flex items-center justify-center text-primary-foreground shadow-md">
                            <Package className="w-6 h-6" />
                          </div>
                          <div className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                            {index + 1}
                          </div>
                        </div>
                        <div className="text-left space-y-2">
                          <div className="flex items-center gap-3">
                            <h3 className="text-lg font-bold text-foreground">
                              {flexPackage.packageName}
                            </h3>
                            <Badge
                              variant={
                                pkgStatus === "completed"
                                  ? "secondary"
                                  : "default"
                              }
                              className={cn(
                                "text-xs",
                                pkgStatus === "completed"
                                  ? "bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400"
                                  : "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                              )}
                            >
                              {statusLabel}
                            </Badge>
                          </div>
                          <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground">
                            <span className="flex items-center gap-2">
                              <Target className="w-5 h-5" />
                              {flexPackage.remainingSessions} /{" "}
                              {flexPackage.totalSessions} seans
                            </span>
                            <span className="flex items-center gap-2">
                              <TrendingUp className="w-5 h-5" />
                              Bu hafta {completedThisWeek} seans
                            </span>
                            <span
                              className={`flex items-center gap-2 font-medium ${expiryColor}`}
                            >
                              <Clock className="w-5 h-5" />
                              {pkgStatus === "completed"
                                ? "Tamamlandı"
                                : `${remainingDays} gün kaldı`}
                            </span>
                          </div>
                          <div className="w-48 h-2 bg-secondary rounded-full overflow-hidden">
                            <div
                              className={cn(
                                "h-full rounded-full transition-all duration-500 ease-out",
                                pkgStatus === "completed"
                                  ? "bg-gradient-to-r from-emerald-500 to-emerald-400"
                                  : "bg-gradient-to-r from-primary to-primary/70"
                              )}
                              style={{ width: `${progressPct}%` }}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-wrap items-center gap-5 w-full lg:w-auto">
                        <div className="flex flex-wrap items-center gap-4">
                          <Badge
                            variant="secondary"
                            className="bg-primary/10 text-primary border-primary/20 font-semibold py-1.5 px-3 text-sm"
                          >
                            {scheduledSessions.length} Planlanmış
                          </Badge>
                          <div className="space-y-1">
                            <p className="text-sm text-muted-foreground">
                              Son kullanma: {formatDate(flexPackage.expiryDate)}
                            </p>
                            <p className={`text-sm font-medium ${expiryColor}`}>
                              {remainingDays} gün süre kaldı
                            </p>
                          </div>
                        </div>
                        <ChevronDown
                          className={cn(
                            "w-5 h-5 text-muted-foreground transition-all duration-300 group-hover:text-foreground ml-auto lg:ml-0",
                            isExpanded && "rotate-180"
                          )}
                        />
                      </div>
                    </div>
                  </CollapsibleTrigger>
                  <CollapsibleContent className="border-t border-border/30 bg-secondary/5">
                    <div className="p-4 space-y-4">
                      {scheduledSessions.length === 0 ? (
                        <div className="text-center py-6 space-y-4">
                          <Calendar className="w-10 h-10 mx-auto text-muted-foreground/40" />
                          <div>
                            <p className="font-medium text-foreground text-lg">
                              Planlanmış randevu bulunmuyor
                            </p>
                            <p className="text-muted-foreground">
                              {flexPackage.memberPackage.status === "completed"
                                ? "Bu paket tamamlanmıştır"
                                : "Yeni bir randevu alarak başlayın"}
                            </p>
                          </div>
                          {flexPackage.memberPackage.status !== "completed" && (
                            <Link href="/appointments/new">
                              <Button variant="outline" size="sm">
                                <Plus className="w-4 h-4 mr-2" />
                                Randevu Al
                              </Button>
                            </Link>
                          )}
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <h4 className="font-semibold text-foreground flex items-center gap-2 text-lg">
                            <Calendar className="w-5 h-5 text-primary" />
                            Yaklaşan Randevular
                          </h4>
                          {scheduledSessions
                            .sort(
                              (a, b) =>
                                new Date(a.session_date).getTime() -
                                new Date(b.session_date).getTime()
                            )
                            .map((session, sessionIndex) => (
                              <SessionItem
                                key={session.id}
                                session={session}
                                packageName={flexPackage.packageName}
                                onDelete={() =>
                                  handleDeleteSession(
                                    session.id,
                                    session.session_date,
                                    session.session_time
                                  )
                                }
                                canDelete={canDeleteSession(
                                  session.session_date,
                                  session.session_time
                                )}
                                index={sessionIndex}
                              />
                            ))}
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              </motion.div>
            );
          })}
        </div>
      )}
    </motion.section>
  );
}
