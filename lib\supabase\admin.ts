import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

/**
 * Admin client for server-side operations with full database access.
 * Uses the service role key to bypass Row Level Security (RLS).
 *
 * WARNING: This client has full access to your database.
 * Only use it in secure server-side environments.
 * Never expose the service role key to the client side.
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    throw new Error(
      "Missing required environment variables: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY"
    );
  }

  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });
}

/**
 * Convenience function to get the admin client instance.
 * Use this when you need to perform administrative operations
 * that require bypassing RLS or managing user accounts.
 */
export function getAdminClient() {
  return createAdminClient();
}
