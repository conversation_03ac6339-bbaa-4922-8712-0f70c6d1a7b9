import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>rk<PERSON>, ArrowRight, MessageCircle } from "lucide-react";

export default function Hero({ whatsappUrl }: { whatsappUrl: string }) {
  return (
    <section className="relative overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-12 md:py-16 lg:py-20 grid lg:grid-cols-2 gap-10 items-center">
        <div className="space-y-6">
          <Badge className="w-fit" variant="gradient">Ka<PERSON><PERSON><PERSON>lara Özel Butik Stüdyo</Badge>
          <h1 className="hero-title">
            Güçlü. Özgüvenli. Sağlıklı.
            <span className="block modern-gradient-text">Loca Fit Studio</span>
          </h1>
          <p className="hero-subtitle max-w-2xl">
            <PERSON><PERSON><PERSON><PERSON> antrenman, küçük grup dersleri ve esnek üyeliklerle hedeflerine güvenle ulaş. İlk dersin bizden.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button asChild size="xl" variant="gradient" className="group">
              <Link href="/appointments/new">
                <Sparkles className="mr-2 size-5 group-hover:rotate-12 transition-transform" />
                Ücretsiz Deneme Başlat
                <ArrowRight className="ml-2 size-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button asChild size="xl" variant="outline" className="group">
              <a href={whatsappUrl} target="_blank" rel="noopener noreferrer" aria-label="WhatsApp ile danış">
                <MessageCircle className="mr-2 size-5 group-hover:scale-110 transition-transform" />
                WhatsApp ile Danış
              </a>
            </Button>
          </div>
        </div>

        <div className="relative">
          <div className="relative w-full aspect-[4/3] rounded-2xl overflow-hidden shadow-soft">
            <Image
              src="https://images.unsplash.com/photo-1605296867304-46d5465a13f1"
              alt="Stüdyoda çalışan kadın sporcu"
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
          <p className="sr-only">Kahraman alanı görsel içerik barındırır.</p>
        </div>
      </div>
    </section>
  );
}

