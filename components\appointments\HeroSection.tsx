"use client";

import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Calendar } from "lucide-react";
import type { Member } from "@/lib/supabase/types";

const fadeInUp = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
};

interface HeroSectionProps {
  memberInfo: Member | null;
}

export default function HeroSection({ memberInfo }: HeroSectionProps) {
  return (
    <motion.section
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      className="text-center space-y-6 pb-4"
    >
      <div className="space-y-4">
        <motion.h1
          className="text-4xl sm:text-5xl md:text-6xl font-bold bg-gradient-to-r from-foreground via-foreground to-muted-foreground bg-clip-text text-transparent"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <PERSON><PERSON><PERSON><PERSON>, {memberInfo?.first_name || "Üye"}!
        </motion.h1>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4"
      >
        <Link href="/appointments/new">
          <Button
            size="lg"
            className="shadow-lg hover:shadow-xl transition-all px-8 py-6 text-lg rounded-xl bg-gradient-to-r from-primary to-primary/80"
          >
            <Calendar className="w-5 h-5 mr-2" />
            Yeni Randevu Al
          </Button>
        </Link>
      </motion.div>
    </motion.section>
  );
}
