"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { toast } from "sonner";
import {
  getMemberFromPhone,
  autoCompletePastSessions,
  getSessionWithDetails,
  updateSession,
  checkTimeSlotCapacity,
  checkExistingAppointment,
  getMemberScheduledSessions,
} from "@/lib/actions/database";
import type { FlexPackageSessionWithDetails } from "@/lib/supabase/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  Package,
  Edit3,
  ArrowLeft,
  CheckCircle2,
  Loader2,
  AlertTriangle,
  Save,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useParams } from "next/navigation";
import { useRouter } from "next/navigation";
import { canDeleteSession } from "@/lib/flex-packages-utils";
import { useMemberPhone, useMemberInfo } from "@/stores/appointments-store";
import {
  formatDate,
  formatTime,
  isTimeSlotInPast,
} from "@/components/appointments/shared/appointment-utils";
import DatePicker from "@/components/appointments/shared/DatePicker";
import TimeSlotPicker from "@/components/appointments/shared/TimeSlotPicker";
import { useAppointmentCapacity } from "@/components/appointments/shared/AppointmentCapacityHook";

interface EditableSession extends FlexPackageSessionWithDetails {
  originalDate: string;
  originalTime: string;
}

export default function EditAppointmentPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params?.id as string;

  // Get member data from context
  const memberPhone = useMemberPhone();
  const memberInfo = useMemberInfo();

  // Step management
  const [currentStep, setCurrentStep] = useState(1);

  const [session, setSession] = useState<EditableSession | null>(null);
  const [selectedDate, setSelectedDate] = useState("");
  const [selectedTime, setSelectedTime] = useState("");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [canEdit, setCanEdit] = useState(false);
  const [bookedDates, setBookedDates] = useState<string[]>([]);

  // Use shared appointment capacity hook
  const { timeSlotCapacity, loadingTimeSlots, fetchTimeSlotCapacities } =
    useAppointmentCapacity();

  // Ref to prevent infinite loop
  const dateChangeRef = useRef(false);

  // Step navigation functions
  const goToNextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const canProceedToNextStep = () => {
    if (currentStep === 1) return canEdit; // Can proceed if appointment is editable
    if (currentStep === 2) return selectedDate !== "" && selectedTime !== "";
    return false;
  };

  // Handle date selection and time slot capacity fetching
  const handleDateSelection = (date: string) => {
    setSelectedDate(date);
    dateChangeRef.current = true;

    // Eğer seçilen tarihte mevcut saat geçmişteyse, saat seçimini sıfırla
    if (selectedTime && isTimeSlotInPast(date, selectedTime)) {
      // Orijinal randevu değilse saat seçimini sıfırla
      const isOriginalSlot =
        selectedTime === session?.originalTime &&
        date === session?.originalDate;
      if (!isOriginalSlot) {
        setSelectedTime("");
        toast.warning(
          "Seçilen saat geçmişte kaldığı için saat seçimi sıfırlandı."
        );
      }
    }
  };

  // Fetch session data function
  const fetchSessionData = useCallback(
    async (phone: string) => {
      try {
        // Auto-complete past sessions first to ensure current status
        await autoCompletePastSessions();

        const member = await getMemberFromPhone(phone);
        if (!member) {
          toast.error("Üye bulunamadı");
          router.push("/appointments");
          return;
        }

        // Session bilgilerini getir
        const sessionData = await getSessionWithDetails(sessionId, member.id);

        if (!sessionData) {
          toast.error("Randevu bulunamadı");
          router.push("/appointments");
          return;
        }

        const editableSession: EditableSession = {
          ...sessionData,
          originalDate: sessionData.session_date,
          originalTime: sessionData.session_time,
        } as EditableSession;

        setSession(editableSession);
        setSelectedDate(sessionData.session_date);
        setSelectedTime(sessionData.session_time);

        // Düzenleme yetkisi kontrolü (12 saat kuralı)
        const canEditSession = canDeleteSession(
          sessionData.session_date,
          sessionData.session_time
        );
        setCanEdit(canEditSession);

        if (!canEditSession) {
          toast.warning(
            "Randevu saatine 12 saatten az kaldığı için düzenlenemez"
          );
        }

        // Member'ın mevcut randevularını getir (sadece scheduled olanlar)
        // Orijinal randevu tarihini hariç tut
        const bookedSessionDates = await getMemberScheduledSessions(member.id);
        const filteredBookedDates = bookedSessionDates.filter(
          (date) => date !== sessionData.session_date
        );
        setBookedDates(filteredBookedDates);
      } catch (error) {
        console.error("Error fetching session:", error);
        toast.error("Randevu bilgileri yüklenirken hata oluştu");
        router.push("/appointments");
      } finally {
        setLoading(false);
      }
    },
    [sessionId, router]
  );

  useEffect(() => {
    // Context'ten telefon numarasını al
    if (!memberPhone) {
      router.push("/appointments");
      return;
    }
    fetchSessionData(memberPhone);
  }, [sessionId, router, fetchSessionData, memberPhone]);

  // Tarih değiştiğinde kapasiteleri getir - FIXED INFINITE LOOP
  useEffect(() => {
    if (selectedDate && canEdit && currentStep === 2 && dateChangeRef.current) {
      dateChangeRef.current = false; // Reset the flag
      fetchTimeSlotCapacities(selectedDate);
    }
  }, [selectedDate, canEdit, currentStep, fetchTimeSlotCapacities]);

  async function handleUpdateAppointment() {
    if (!selectedDate || !selectedTime || !session) {
      toast.error("Lütfen tüm alanları doldurun");
      return;
    }

    if (!canEdit) {
      toast.error("Bu randevu düzenlenemez");
      return;
    }

    // Değişiklik var mı kontrol et
    if (
      selectedDate === session.originalDate &&
      selectedTime === session.originalTime
    ) {
      toast.warning(
        "Değişiklik yapabilmek için farklı bir tarih veya saat seçmelisiniz."
      );
      return;
    }

    // Eğer tarih değişiyorsa aynı güne randevu kontrolü yap
    if (selectedDate !== session.originalDate) {
      // Member ID'yi al
      if (!memberPhone) {
        toast.error("Üye bilgisi bulunamadı");
        return;
      }
      const member = await getMemberFromPhone(memberPhone);
      if (!member) {
        toast.error("Üye bulunamadı");
        return;
      }

      // Aynı güne randevu kontrolü - sadece tarih değiştiyse
      const hasExistingAppointment = await checkExistingAppointment(
        member.id,
        selectedDate
      );

      if (hasExistingAppointment) {
        toast.error(
          "Bu tarihte zaten bir randevunuz bulunmaktadır. Aynı güne birden fazla randevu alamazsınız."
        );
        return;
      }
    }

    // Eğer tarih veya saat değişiyorsa kapasite kontrolü yap
    if (
      selectedDate !== session.originalDate ||
      selectedTime !== session.originalTime
    ) {
      // Geçmiş zaman kontrolü - orijinal randevu değilse geçmiş saatlere izin verme
      const isOriginalSlot =
        selectedDate === session.originalDate &&
        selectedTime === session.originalTime;
      if (!isOriginalSlot && isTimeSlotInPast(selectedDate, selectedTime)) {
        toast.error(
          "Geçmiş saatlere randevu alamazsınız. Lütfen gelecek bir zaman seçin."
        );
        return;
      }

      const capacityCheck = await checkTimeSlotCapacity(
        selectedDate,
        selectedTime
      );

      if (!capacityCheck.available) {
        toast.error(
          `Bu tarih ve saat için kapasite dolu. Şu anda ${capacityCheck.currentCount}/${capacityCheck.maxCapacity} randevu mevcut.`
        );
        return;
      }
    }

    setSaving(true);
    try {
      // Randevuyu güncelle
      const success = await updateSession(
        sessionId,
        selectedDate,
        selectedTime
      );

      if (!success) {
        throw new Error("Randevu güncellenemedi");
      }

      toast.success("Randevu başarıyla güncellendi!");

      // Ana sayfaya yönlendir
      setTimeout(() => {
        router.push("/appointments");
      }, 1500);
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Randevu güncellenirken hata oluştu");
    } finally {
      setSaving(false);
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-modern">
        <div className="container mx-auto p-4">
          <div className="flex justify-center items-center min-h-[60vh]">
            <div className="text-center space-y-4">
              <Loader2 className="w-8 h-8 animate-spin mx-auto text-brand" />
              <div className="text-lg font-medium">
                Randevu bilgileri yükleniyor...
              </div>
              <div className="text-sm text-muted-foreground">
                Lütfen bekleyin
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-modern">
        <div className="container mx-auto p-4">
          <div className="flex flex-col items-center justify-center min-h-[60vh] space-y-6">
            <div className="relative">
              <div className="w-20 h-20 bg-muted/50 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-10 h-10 text-muted-foreground" />
              </div>
            </div>
            <div className="text-center space-y-3 max-w-md">
              <h2 className="text-2xl font-bold">Randevu Bulunamadı</h2>
              <p className="text-muted-foreground leading-relaxed">
                Aradığınız randevu bulunamadı veya bu randevuyu düzenleme
                yetkiniz bulunmuyor.
              </p>
            </div>
            <Button
              onClick={() => router.push("/appointments")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Randevularıma Dön
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-modern">
      <div className="container mx-auto p-4 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8 pt-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-brand/20 rounded-lg">
              <Edit3 className="w-6 h-6 text-brand" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Randevu Düzenle</h1>
              <p className="text-muted-foreground">
                Adım {currentStep}/3:{" "}
                {currentStep === 1
                  ? "Mevcut Bilgiler"
                  : currentStep === 2
                  ? "Tarih ve Saat Seçimi"
                  : "Önizleme"}
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => router.push("/appointments")}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Geri
          </Button>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",
                    currentStep >= step
                      ? "bg-brand text-white"
                      : "bg-secondary text-muted-foreground"
                  )}
                >
                  {currentStep > step ? (
                    <CheckCircle2 className="w-5 h-5" />
                  ) : (
                    step
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <div
                    className={cn(
                      "text-sm font-medium",
                      currentStep >= step
                        ? "text-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    {step === 1 && "Mevcut Bilgiler"}
                    {step === 2 && "Tarih & Saat"}
                    {step === 3 && "Önizleme"}
                  </div>
                </div>
                {step < 3 && (
                  <div
                    className={cn(
                      "h-1 w-16 mx-4 rounded-full transition-all duration-300",
                      currentStep > step ? "bg-brand" : "bg-secondary"
                    )}
                  />
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-8">
          {/* Step 1: Current Appointment Information */}
          {currentStep === 1 && (
            <Card
              variant="elevated"
              className="relative overflow-hidden border-l-4 border-l-info"
            >
              <CardHeader className="bg-info/10">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-info/20 rounded-lg">
                      <Package className="w-5 h-5 text-info" />
                    </div>
                    <div>
                      <span className="text-lg text-info">
                        Mevcut Randevu Bilgileri
                      </span>
                      <p className="text-sm text-info-foreground font-normal">
                        Şu anki randevu detaylarınız
                      </p>
                    </div>
                  </div>
                  {canEdit && <CheckCircle2 className="w-5 h-5 text-success" />}
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="bg-muted/50 p-6 rounded-xl border space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">Paket</div>
                      <div className="font-semibold text-lg">
                        {session.member_package?.flex_package?.name}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        Telefon
                      </div>
                      <div className="font-semibold text-lg">{memberPhone}</div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        Mevcut Tarih
                      </div>
                      <div className="font-semibold text-lg flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        {formatDate(session.originalDate)}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="text-sm text-muted-foreground">
                        Mevcut Saat
                      </div>
                      <div className="font-semibold text-lg flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        {formatTime(session.originalTime)}
                      </div>
                    </div>
                  </div>

                  {!canEdit && (
                    <div className="bg-warning/10 border border-warning/20 rounded-lg p-4 mt-4">
                      <div className="flex items-center gap-2 text-warning">
                        <AlertTriangle className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          Randevu saatine 12 saatten az kaldığı için
                          düzenlenemez
                        </span>
                      </div>
                    </div>
                  )}
                </div>

                {/* Step 1 Navigation */}
                <div className="flex justify-end mt-6">
                  <Button
                    onClick={goToNextStep}
                    disabled={!canProceedToNextStep()}
                    className="flex items-center gap-2"
                  >
                    Sonraki Adım
                    <CheckCircle2 className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Step 2: Date and Time Selection */}
          {currentStep === 2 && (
            <>
              {/* Date Selection */}
              <Card
                variant="elevated"
                className="relative overflow-hidden border-l-4 border-l-brand/50"
              >
                <CardHeader className="bg-muted/30">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-brand/20 rounded-lg">
                        <Calendar className="w-5 h-5 text-brand" />
                      </div>
                      <div>
                        <span className="text-lg">Yeni Tarih Seçimi</span>
                        <p className="text-sm text-muted-foreground font-normal">
                          Randevunuz için yeni bir tarih seçin
                        </p>
                      </div>
                    </div>
                    {selectedDate && selectedDate !== session.originalDate && (
                      <CheckCircle2 className="w-5 h-5 text-success" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <DatePicker
                    selectedDate={selectedDate}
                    onDateSelect={handleDateSelection}
                    originalDate={session.originalDate}
                    isEditMode={true}
                    bookedDates={bookedDates}
                  />
                </CardContent>
              </Card>

              {/* Time Selection */}
              {selectedDate && (
                <Card
                  variant="elevated"
                  className="relative overflow-hidden border-l-4 border-l-brand/50"
                >
                  <CardHeader className="bg-muted/30">
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-brand/20 rounded-lg">
                          <Clock className="w-5 h-5 text-brand" />
                        </div>
                        <div>
                          <span className="text-lg">Yeni Saat Seçimi</span>
                          <p className="text-sm text-muted-foreground font-normal">
                            Randevunuz için yeni bir saat seçin
                          </p>
                        </div>
                      </div>
                      {selectedTime &&
                        selectedTime !== session.originalTime && (
                          <CheckCircle2 className="w-5 h-5 text-success" />
                        )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <TimeSlotPicker
                      selectedDate={selectedDate}
                      selectedTime={selectedTime}
                      onTimeSelect={(time) => {
                        // Mevcut randevu saatinin tekrar seçilmesini engelle
                        const normalizedOriginalTime =
                          session.originalTime?.slice(0, 5);
                        const isOriginalSlot =
                          time === normalizedOriginalTime &&
                          selectedDate === session.originalDate;

                        if (isOriginalSlot) {
                          toast.warning(
                            "Bu randevunuzun mevcut saati. Değişiklik yapmak için farklı bir saat seçin."
                          );
                          return;
                        }

                        // Geçmiş saatlerin seçilmesini engelle
                        const isPastTime = isTimeSlotInPast(selectedDate, time);
                        if (isPastTime) {
                          toast.error(
                            "Geçmiş saatlere randevu alamazsınız. Lütfen gelecek bir zaman seçin."
                          );
                          return;
                        }

                        // Dolu saatlerin seçilmesini engelle
                        const capacity = timeSlotCapacity[time];
                        const isFull = capacity && !capacity.available;
                        if (isFull) {
                          toast.error(
                            `Bu saat için kapasite dolu. ${capacity?.currentCount}/${capacity?.maxCapacity} randevu mevcut.`
                          );
                          return;
                        }

                        setSelectedTime(time);
                      }}
                      timeSlotCapacity={timeSlotCapacity}
                      loadingTimeSlots={loadingTimeSlots}
                      originalTime={session.originalTime?.slice(0, 5)}
                      originalDate={session.originalDate}
                      isEditMode={true}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Step 2 Navigation */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={goToPreviousStep}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Önceki Adım
                </Button>
                <Button
                  onClick={goToNextStep}
                  disabled={!canProceedToNextStep()}
                  className="flex items-center gap-2"
                >
                  Sonraki Adım
                  <CheckCircle2 className="w-4 h-4" />
                </Button>
              </div>
            </>
          )}

          {/* Step 3: Review and Confirmation */}
          {currentStep === 3 && selectedDate && selectedTime && (
            <Card
              variant="elevated"
              className="relative overflow-hidden border-l-4 border-l-success"
            >
              <CardHeader className="bg-gradient-to-r from-success/10 to-success/5">
                <CardTitle className="flex items-center space-x-3">
                  <div className="p-2 bg-success/20 rounded-lg">
                    <CheckCircle2 className="w-5 h-5 text-success" />
                  </div>
                  <div>
                    <span className="text-lg text-success">
                      Güncelleme Özeti
                    </span>
                    <p className="text-sm text-success-foreground font-normal">
                      Değişiklikleri kontrol edin
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div className="bg-muted/50 p-6 rounded-xl border space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="font-semibold text-base text-muted-foreground">
                          Önceki Bilgiler
                        </h3>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="w-4 h-4" />
                            {formatDate(session.originalDate)}
                          </div>
                          <div className="flex items-center gap-2 text-sm">
                            <Clock className="w-4 h-4" />
                            {formatTime(session.originalTime)}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h3 className="font-semibold text-base text-success">
                          Yeni Bilgiler
                        </h3>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 text-sm font-medium">
                            <Calendar className="w-4 h-4" />
                            {formatDate(selectedDate)}
                          </div>
                          <div className="flex items-center gap-2 text-sm font-medium">
                            <Clock className="w-4 h-4" />
                            {formatTime(selectedTime)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Step 3 Navigation */}
                  <div className="flex justify-between">
                    <Button
                      variant="outline"
                      onClick={goToPreviousStep}
                      className="flex items-center gap-2"
                      disabled={saving}
                    >
                      <ArrowLeft className="w-4 h-4" />
                      Önceki Adım
                    </Button>
                    <div className="flex gap-3">
                      <Button
                        onClick={handleUpdateAppointment}
                        disabled={
                          saving ||
                          (selectedDate === session.originalDate &&
                            selectedTime === session.originalTime)
                        }
                        className="flex items-center gap-2 h-12 text-lg font-medium"
                        size="lg"
                        variant="gradient"
                      >
                        {saving ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                            Güncelleniyor...
                          </>
                        ) : selectedDate === session.originalDate &&
                          selectedTime === session.originalTime ? (
                          <>
                            <AlertTriangle className="w-4 h-4 mr-2" />
                            Değişiklik Yapın
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4 mr-2" />
                            Değişiklikleri Kaydet
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push("/appointments")}
                        className="h-12 px-8"
                        size="lg"
                        disabled={saving}
                      >
                        İptal
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
