import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { LotusIcon, LotusSimpleIcon } from "@/components/ui/lotus-icon";
import {
  Activity,
  ArrowRight,
  Award,
  Check,
  CheckCircle,
  ChevronRight,
  Clock,
  Heart,
  HeartPulse,
  MessageCircle,
  Phone,
  ShieldCheck,
  Sparkles,
  Star,
  Target,
  TrendingUp,
  Trophy,
  Users,
  Waves,
  Zap,
} from "lucide-react";
import NavBar from "@/components/homepage/NavBar";
import Hero from "@/components/homepage/Hero";
import Programs from "@/components/homepage/Programs";
import WhyUs from "@/components/homepage/WhyUs";
import Pricing from "@/components/homepage/Pricing";
import Testimonials from "@/components/homepage/Testimonials";
import FAQ from "@/components/homepage/FAQ";
import Location from "@/components/homepage/Location";
import Footer from "@/components/homepage/Footer";

export default function Page() {
  // Mock iletişim
  const phone = "+905320000000";
  const whatsapp = "https://wa.me/905320000000";
  const email = "<EMAIL>";
  const address = "Bağdat Caddesi No:123, Kadıköy, İstanbul";
  const instagram = "https://instagram.com/locafitstudio";

  // Yeni ana sayfa tasarımı — eski içerik yerine bu bölüm render edilir
  return (
    <>
      <a
        href="#ana-icerik"
        className="sr-only focus:not-sr-only focus:absolute focus:p-2 focus:m-2 focus:bg-white dark:focus:bg-black focus:rounded"
      >
        Icerige atla
      </a>
      <main id="ana-icerik" className="min-h-[100svh] bg-gradient-modern">
        <NavBar phone={phone} />
        <Hero whatsappUrl={whatsapp} />
        <Programs />
        <WhyUs />
        <Pricing />
        <Testimonials />
        <FAQ />
        <Location
          address={address}
          phone={phone}
          email={email}
          whatsappUrl={whatsapp}
        />
        <Footer />
      </main>

      <a
        href={whatsapp}
        target="_blank"
        rel="noopener noreferrer"
        aria-label="WhatsApp ile hizli iletisim"
        className="fixed bottom-6 right-6 z-50"
      >
        <Button
          variant="gradient"
          size="xl"
          className="rounded-full shadow-soft"
        >
          <MessageCircle className="mr-2" />
          WhatsApp
        </Button>
      </a>
    </>
  );

  // Mock slider görselleri (Unsplash - next.config.ts uyumlu)
  const heroImages = [
    {
      src: "https://images.unsplash.com/photo-1605296867304-46d5465a13f1",
      alt: "Kadın sporcu ağırlık çalışırken",
    },
    {
      src: "https://images.unsplash.com/photo-1546483875-ad9014c88eba",
      alt: "Yoga yapan kadın grubu",
    },
    {
      src: "https://images.unsplash.com/photo-1518611012118-696072aa579a",
      alt: "Zumba ile ritim ve kardiyo",
    },
  ];

  // Mock hizmetler
  const services = [
    {
      title: "Pilates (Grup)",
      desc: "Esneklik ve core gücünü artıran, tüm seviyelere uygun dersler.",
      icon: HeartPulse,
    },
    {
      title: "Yoga (Grup)",
      desc: "Zihin ve beden uyumu; denge, nefes ve akış odaklı seanslar.",
      icon: Waves,
    },
    {
      title: "Zumba (Grup)",
      desc: "Eğlenceli ritimler eşliğinde yüksek enerji ve kardiyo.",
      icon: Activity,
    },
    {
      title: "HIIT (Grup)",
      desc: "Kısa sürede yüksek verim: yağ yakımı ve kondisyona odak.",
      icon: Zap,
    },
    {
      title: "Kişisel Antrenörlük",
      desc: "Hedefine özel planlama ve birebir takip ile hızlı sonuç.",
      icon: ShieldCheck,
    },
    {
      title: "Beslenme Danışmanlığı",
      desc: "Performansı destekleyen pratik ve sürdürülebilir planlar.",
      icon: Check,
    },
  ];

  // Mock neden biz
  const reasons = [
    {
      title: "Sadece Kadınlara Özel",
      desc: "Güvenli, destekleyici ve kapsayıcı stüdyo ortamı.",
      icon: ShieldCheck,
    },
    {
      title: "Deneyimli Kadın Antrenörler",
      desc: "Uzman kadro ile hedefe giden yolda doğru rehberlik.",
      icon: Check,
    },
    {
      title: "Esnek Ders Saatleri",
      desc: "Yoğun tempona uyumlu, hafta içi/sonu geniş zamanlar.",
      icon: Clock,
    },
    {
      title: "Modern Ekipmanlar",
      desc: "Hijyenik, bakımlı ve performansı artıran ekipmanlar.",
      icon: Activity,
    },
  ];

  // Mock yorumlar - Genişletilmiş
  const testimonials = [
    {
      name: "Duygu K.",
      tag: "Pilates",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      content:
        "3 ayda bel ağrılarım ciddi şekilde azaldı. Eğitmenler çok ilgili ve motive edici. Artık kendimi çok daha güçlü hissediyorum!",
      achievement: "15 kg verdi",
    },
    {
      name: "Sevgi A.",
      tag: "Yoga",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      content:
        "Zihin ve beden dengesini burada buldum. Dersler sonrası enerji tavan! Stresim azaldı, uyku kalitem arttı.",
      achievement: "6 ay düzenli",
    },
    {
      name: "Elif T.",
      tag: "Zumba",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=150&h=150&fit=crop&crop=face",
      content:
        "Her ders parti gibi geçiyor. Eğlenerek kalori yakmak bu kadar kolaymış! Arkadaşlarım değişimimi fark etti.",
      achievement: "20 kg verdi",
    },
    {
      name: "Ayşe M.",
      tag: "HIIT",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
      content:
        "Kısa sürede inanılmaz sonuçlar aldım. Kondisyonum çok arttı, günlük hayatımda çok daha enerjik hissediyorum.",
      achievement: "3 ay transformasyon",
    },
    {
      name: "Zeynep D.",
      tag: "Kişisel Antrenman",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=150&h=150&fit=crop&crop=face",
      content:
        "Birebir antrenman sayesinde hedeflerime çok hızlı ulaştım. Antrenörüm her adımda yanımda oldu.",
      achievement: "Hedef kiloya ulaştı",
    },
    {
      name: "Fatma S.",
      tag: "Beslenme",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face",
      content:
        "Beslenme programı hayatımı değiştirdi. Sağlıklı yemek yapmayı öğrendim, enerji seviyem arttı.",
      achievement: "Yaşam tarzı değişimi",
    },
  ];

  // İstatistikler
  const stats = [
    {
      number: "1000+",
      label: "Mutlu Üye",
      icon: Users,
      color: "text-pink-500",
    },
    {
      number: "50+",
      label: "Uzman Antrenör",
      icon: Award,
      color: "text-purple-500",
    },
    {
      number: "5000+",
      label: "Tamamlanan Ders",
      icon: TrendingUp,
      color: "text-rose-500",
    },
    {
      number: "98%",
      label: "Memnuniyet Oranı",
      icon: Heart,
      color: "text-pink-600",
    },
  ];

  // Özellikler
  const features = [
    {
      title: "Kadın Dostu Ortam",
      description:
        "Sadece kadınlara özel, güvenli ve destekleyici stüdyo atmosferi",
      icon: Heart,
      gradient: "from-pink-500 to-rose-500",
    },
    {
      title: "Uzman Kadın Antrenörler",
      description:
        "Alanında uzman, deneyimli kadın antrenörlerden profesyonel rehberlik",
      icon: Award,
      gradient: "from-purple-500 to-pink-500",
    },
    {
      title: "Esnek Program Seçenekleri",
      description:
        "Yoğun programınıza uygun, esnek ders saatleri ve çeşitli program seçenekleri",
      icon: Clock,
      gradient: "from-rose-500 to-pink-500",
    },
    {
      title: "Kişiselleştirilmiş Yaklaşım",
      description: "Size özel hazırlanan antrenman ve beslenme programları",
      icon: Target,
      gradient: "from-pink-600 to-rose-600",
    },
    {
      title: "Modern Ekipman & Hijyen",
      description: "Son teknoloji ekipmanlar ve yüksek hijyen standartları",
      icon: Sparkles,
      gradient: "from-purple-600 to-pink-600",
    },
    {
      title: "Topluluk Desteği",
      description: "Motivasyonu yüksek tutan, destekleyici kadın topluluğu",
      icon: Users,
      gradient: "from-rose-600 to-pink-600",
    },
  ];

  return (
    <main className="min-h-[100svh] bg-gradient-modern">
      {/* Header */}
      <header className="sticky top-0 z-50 backdrop-blur-xl glass-card-modern border-b">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 h-16 flex items-center justify-between">
          <Link href="/" className="flex items-center gap-2">
            <LotusIcon size={32} className="animate-pulse-glow" />
            <span className="font-extrabold tracking-tight text-gradient-modern">
              Loca Fit Studio
            </span>
          </Link>

          <nav className="hidden md:flex items-center gap-6 text-sm">
            <Link href="#hizmetler" className="hover:opacity-80">
              Hizmetler
            </Link>
            <Link href="#neden" className="hover:opacity-80">
              Neden Biz
            </Link>
            <Link href="#yorumlar" className="hover:opacity-80">
              Yorumlar
            </Link>
            <Link href="#iletisim" className="hover:opacity-80">
              İletişim
            </Link>
          </nav>

          <div className="flex items-center gap-2">
            <ThemeToggle />
            <Button
              asChild
              variant="gradient"
              size="xl"
              className="hidden md:inline-flex"
            >
              <Link href="/appointments">Giriş Yap</Link>
            </Button>
            <Button
              asChild
              variant="glass"
              size="icon"
              aria-label="Telefon ile ara"
            >
              <a href={`tel:${phone}`}>
                <Phone className="size-5" />
              </a>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero */}
      <section className="relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-12 md:py-16 lg:py-20">
          <div className="grid lg:grid-cols-2 gap-10 items-center">
            <div className="space-y-6">
              <div className="modern-badge inline-flex items-center gap-2">
                <LotusSimpleIcon size={16} className="text-pink-500" />
                Kadınlara Özel Spor Alanı
              </div>
              <h1 className="hero-title">
                Güçlü, Özgüvenli ve Sağlıklı Kadınlar İçin{" "}
                <span className="modern-gradient-text">Özel Alan</span>
              </h1>
              <p className="hero-subtitle max-w-2xl">
                Sadece kadınlara özel, uzman antrenörlerle hedeflerine daha
                hızlı ulaş. Modern ekipmanlar, esnek saatler ve topluluk
                desteğiyle motivasyon hep yüksek.
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  asChild
                  variant="gradient"
                  size="xl"
                  className="hover-lift group"
                >
                  <Link href="/appointments/new">
                    <Sparkles className="mr-2 size-5 group-hover:rotate-12 transition-transform" />
                    Ücretsiz Deneme Başlat
                    <ArrowRight className="ml-2 size-5 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="glass"
                  size="xl"
                  className="hover-lift group"
                >
                  <a
                    href={whatsapp}
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label="WhatsApp ile yaz"
                  >
                    <MessageCircle className="mr-2 size-5 group-hover:scale-110 transition-transform" />
                    Hemen Danış
                    <ChevronRight className="ml-2 size-4 group-hover:translate-x-1 transition-transform" />
                  </a>
                </Button>
              </div>

              {/* Hero Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 pt-6 border-t border-white/20">
                {stats.slice(0, 4).map((stat, i) => (
                  <div key={i} className="text-center">
                    <div className={`text-2xl font-bold ${stat.color}`}>
                      {stat.number}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex flex-wrap items-center gap-5 text-sm text-muted-foreground">
                <a
                  className="inline-flex items-center gap-2 hover:opacity-80"
                  href={`tel:${phone}`}
                >
                  <Phone className="size-4" />
                  <span>{phone}</span>
                </a>
                <span className="inline-flex items-center gap-2">
                  <Clock className="size-4" /> Hafta içi 08:00–22:00, Hafta sonu
                  09:00–20:00
                </span>
              </div>
            </div>

            {/* CSS scroll-snap slider (JS yok, mobil uyumlu) */}
            <div className="relative">
              <div className="flex gap-4 overflow-x-auto snap-x snap-mandatory scrollbar-none pb-2">
                {heroImages.map((img, i) => (
                  <div
                    key={i}
                    className="relative w-[85%] md:w-1/2 lg:w-[48%] aspect-[4/3] rounded-2xl overflow-hidden snap-center glass-card-hero"
                  >
                    <Image
                      src={img.src}
                      alt={img.alt}
                      fill
                      sizes="(max-width: 768px) 85vw, (max-width: 1024px) 50vw, 48vw"
                      priority={i === 0}
                      className="object-cover"
                    />
                  </div>
                ))}
              </div>
              <p className="sr-only">
                Hero alanı, parmakla kaydırılabilen sporcu görselleri içerir.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Hizmetler */}
      <section id="hizmetler" className="section-padding">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="mb-8 md:mb-12">
            <Badge variant="gradient" className="mb-3">
              Programlar
            </Badge>
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">
              Hizmetlerimiz
            </h2>
            <p className="text-muted-foreground mt-2">
              Grup dersleri, birebir çalışmalar ve beslenme desteği ile
              ihtiyacına uygun programı seç.
            </p>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((s, i) => (
              <Card key={i} className="service-card-modern hover-lift">
                <CardHeader className="flex-row items-center gap-3">
                  <div className="size-10 rounded-xl bg-gradient-primary grid place-items-center text-white">
                    <s.icon className="size-5" />
                  </div>
                  <CardTitle className="text-lg">{s.title}</CardTitle>
                </CardHeader>
                <CardContent className="pt-2">
                  <CardDescription>{s.desc}</CardDescription>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button asChild variant="soft">
                    <Link href="/appointments/new">Ücretsiz Deneme</Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Neden Biz */}
      <section id="neden" className="section-padding">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="mb-8 md:mb-12">
            <Badge variant="soft" className="mb-3">
              Farkımız
            </Badge>
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">
              Neden Loca Fit Studio?
            </h2>
          </div>

          <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {reasons.map((r, i) => (
              <Card
                key={i}
                variant="gradient"
                className="glass-card-modern hover-lift"
              >
                <CardHeader>
                  <div className="size-10 rounded-xl bg-white/70 dark:bg-white/10 grid place-items-center">
                    <r.icon className="size-5" />
                  </div>
                  <CardTitle className="mt-2">{r.title}</CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription>{r.desc}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="mt-8 flex flex-wrap items-center gap-6 text-sm">
            <div className="inline-flex items-center gap-2">
              <ShieldCheck className="size-4 text-success" />
              <span>1.000+ mutlu üye</span>
            </div>
            <div className="inline-flex items-center gap-2">
              <Star className="size-4 text-warning" />
              <span>4.9/5 memnuniyet</span>
            </div>
          </div>
        </div>
      </section>

      {/* İstatistikler */}
      <section className="section-padding bg-gradient-to-br from-pink-50/50 to-purple-50/50 dark:from-pink-950/20 dark:to-purple-950/20">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge variant="gradient" className="mb-3">
              Başarılarımız
            </Badge>
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">
              Rakamlarla Loca Fit Studio
            </h2>
            <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
              Binlerce kadının güvendiği, uzman antrenörlerle desteklenen başarı
              hikayemiz
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, i) => (
              <Card
                key={i}
                className="glass-card-modern hover-lift text-center"
              >
                <CardContent className="pt-6">
                  <div
                    className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r ${
                      stat.color === "text-pink-500"
                        ? "from-pink-500 to-rose-500"
                        : stat.color === "text-purple-500"
                        ? "from-purple-500 to-pink-500"
                        : stat.color === "text-rose-500"
                        ? "from-rose-500 to-pink-500"
                        : "from-pink-600 to-rose-600"
                    } text-white mb-4`}
                  >
                    <stat.icon className="size-6" />
                  </div>
                  <div className={`text-3xl font-bold ${stat.color} mb-2`}>
                    {stat.number}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Özellikler */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-12">
            <Badge variant="soft" className="mb-3">
              Neden Farklıyız
            </Badge>
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">
              Size Özel Avantajlar
            </h2>
            <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
              Kadınlara özel tasarlanmış hizmetlerimiz ve yaklaşımımızla farkı
              hissedin
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, i) => (
              <Card key={i} className="glass-card-modern hover-lift group">
                <CardContent className="pt-6">
                  <div
                    className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r ${feature.gradient} text-white mb-4 group-hover:scale-110 transition-transform`}
                  >
                    <feature.icon className="size-6" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Sosyal Kanıt */}
      <section
        id="yorumlar"
        className="section-padding bg-gradient-to-br from-rose-50/50 to-pink-50/50 dark:from-rose-950/20 dark:to-pink-950/20"
      >
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
          <div className="mb-8 md:mb-12 text-center">
            <Badge variant="gradient" className="mb-3">
              Başarı Hikayeleri
            </Badge>
            <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">
              Müşteri Yorumları
            </h2>
            <p className="text-muted-foreground mt-2 max-w-2xl mx-auto">
              Binlerce kadının transformasyon hikayesi. Sırada sen varsın!
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testimonials.map((t, i) => (
              <Card key={i} className="glass-card-modern hover-lift">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className="relative w-12 h-12 rounded-full overflow-hidden">
                      <Image
                        src={t.image}
                        alt={t.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{t.name}</CardTitle>
                      <Badge variant="glass" className="text-xs">
                        {t.tag}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0 space-y-3">
                  <div className="flex items-center gap-1 text-warning">
                    {Array.from({ length: 5 }).map((_, idx) => (
                      <Star
                        key={idx}
                        className={`size-4 ${
                          idx < t.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "opacity-30"
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-sm text-muted-foreground">{t.content}</p>
                  <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-gradient-to-r from-pink-500/10 to-rose-500/10 text-xs font-medium text-pink-600 dark:text-pink-400">
                    <Trophy className="size-3" />
                    {t.achievement}
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button asChild variant="soft" className="w-full">
                    <Link href="/appointments/new">
                      Sen de başla — Ücretsiz Deneme
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-gradient-to-r from-pink-600 via-rose-600 to-purple-600 text-white">
        <div className="max-w-4xl mx-auto px-4 md:px-6 lg:px-8 text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm mb-6">
            <LotusSimpleIcon size={20} className="text-white" />
            <span className="text-sm font-medium">Özel Fırsat</span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-extrabold tracking-tight mb-4">
            İlk Dersini <span className="text-yellow-300">Ücretsiz</span> Dene!
          </h2>

          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            Kendine zaman ayırmanın tam zamanı. Uzman antrenörlerimizle tanış,
            stüdyomuzu keşfet ve sana en uygun programı birlikte belirleyelim.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              asChild
              variant="secondary"
              size="xl"
              className="hover-lift group bg-white text-pink-600 hover:bg-white/90"
            >
              <Link href="/appointments/new">
                <Sparkles className="mr-2 size-5 group-hover:rotate-12 transition-transform" />
                Ücretsiz Deneme Rezervasyonu
                <ArrowRight className="ml-2 size-5 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button
              asChild
              variant="outline"
              size="xl"
              className="hover-lift group border-white/30 text-white hover:bg-white/10"
            >
              <a href={whatsapp} target="_blank" rel="noopener noreferrer">
                <MessageCircle className="mr-2 size-5 group-hover:scale-110 transition-transform" />
                WhatsApp ile Danış
                <ChevronRight className="ml-2 size-4 group-hover:translate-x-1 transition-transform" />
              </a>
            </Button>
          </div>

          <div className="mt-8 flex flex-wrap justify-center gap-6 text-sm text-white/80">
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4" />
              Ücretsiz deneme
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4" />
              Taahhüt yok
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="size-4" />
              Uzman rehberlik
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer
        id="iletisim"
        className="section-padding border-t glass-card-modern"
      >
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 grid md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <h3 className="font-bold text-lg mb-3">Loca Fit Studio</h3>
            <p className="text-sm text-muted-foreground max-w-prose">
              Kadınlara özel stüdyoda güçlü ve özgüvenli bir sen için adım at.
              Ücretsiz deneme seansına gel, sana en uygun programı birlikte
              seçelim.
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-3">İletişim</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a
                  className="hover:opacity-80 inline-flex items-center gap-2"
                  href={`tel:${phone}`}
                >
                  <Phone className="size-4" /> {phone}
                </a>
              </li>
              <li>
                <a
                  className="hover:opacity-80 inline-flex items-center gap-2"
                  href={whatsapp}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <MessageCircle className="size-4" /> WhatsApp
                </a>
              </li>
              <li>
                <a className="hover:opacity-80" href={`mailto:${email}`}>
                  {email}
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-3">Adres</h4>
            <p className="text-sm text-muted-foreground">{address}</p>
            <a
              className="text-sm underline mt-2 inline-block hover:opacity-80"
              href={`https://maps.google.com/?q=${encodeURIComponent(address)}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              Haritada Aç
            </a>

            <div className="mt-4">
              <h4 className="font-semibold mb-3">Sosyal Medya</h4>
              <a
                className="text-sm underline hover:opacity-80"
                href={instagram}
                target="_blank"
                rel="noopener noreferrer"
              >
                Instagram
              </a>
            </div>

            <div className="mt-4">
              <h4 className="font-semibold mb-3">Çalışma Saatleri</h4>
              <p className="text-sm text-muted-foreground">
                Hafta içi 08:00–22:00
                <br />
                Hafta sonu 09:00–20:00
              </p>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 mt-8 text-xs text-muted-foreground">
          © {new Date().getFullYear()} Loca Fit Studio. Tüm hakları saklıdır.
        </div>
      </footer>

      {/* Sabit WhatsApp butonu */}
      <a
        href={whatsapp}
        target="_blank"
        rel="noopener noreferrer"
        aria-label="WhatsApp ile hızlı iletişim"
        className="fixed bottom-6 right-6 z-50"
      >
        <Button
          variant="gradient"
          size="xl"
          className="rounded-full shadow-soft"
        >
          <MessageCircle className="mr-2" />
          WhatsApp
        </Button>
      </a>
    </main>
  );
}
