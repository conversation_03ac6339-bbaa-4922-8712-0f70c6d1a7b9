import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const plans = [
  { name: "Esnek Paket", price: "₺950", period: "/10 ders", desc: "Yoğun programlar için esnek kullanım" },
  { name: "Aylık Üyelik", price: "₺1.500", period: "/ay", desc: "Sınırsız grup dersleri" },
  { name: "<PERSON><PERSON>", price: "₺1.200", period: "/seans", desc: "Birebir hedefe yönelik çalışma" },
];

export default function Pricing() {
  return (
    <section id="fiyat" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">Üyelik ve Paketler</h2>
          <p className="text-muted-foreground mt-2">İhtiyacınıza göre esnek seçenekler</p>
        </div>
        <div className="grid md:grid-cols-3 gap-6">
          {plans.map((p) => (
            <Card key={p.name} className="hover-lift">
              <CardHeader>
                <CardTitle>{p.name}</CardTitle>
                <CardDescription>{p.desc}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{p.price}
                  <span className="text-sm text-muted-foreground font-normal"> {p.period}</span>
                </div>
                <div className="mt-6 grid gap-3">
                  <Button asChild variant="gradient">
                    <Link href="/appointments/new">Ücretsiz Deneme</Link>
                  </Button>
                  <Button asChild variant="outline">
                    <Link href="#iletisim">Daha Fazla Bilgi</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

