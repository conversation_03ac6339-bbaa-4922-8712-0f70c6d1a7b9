import { Button } from "@/components/ui/button";

type Props = {
  address: string;
  phone: string;
  email: string;
  whatsappUrl: string;
};

export default function Location({ address, phone, email, whatsappUrl }: Props) {
  return (
    <section id="iletisim" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 grid lg:grid-cols-2 gap-8 items-start">
        <div className="space-y-4">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">İletişim ve Konum</h2>
          <p className="text-muted-foreground">Sorularınız için bize ulaşın veya stüdyomuza uğrayın.</p>
          <div className="rounded-xl overflow-hidden border bg-white/70 dark:bg-white/5">
            <iframe
              title="Loca Fit Studio Harita"
              src="https://www.google.com/maps?q=Kad%C4%B1k%C3%B6y%20%C4%B0stanbul&output=embed"
              className="w-full h-72"
              loading="lazy"
              referrerPolicy="no-referrer-when-downgrade"
            />
          </div>
          <ul className="text-sm text-muted-foreground space-y-2">
            <li><strong>Adres:</strong> {address}</li>
            <li><strong>Telefon:</strong> <a href={`tel:${phone}`} className="underline">{phone}</a></li>
            <li><strong>E-posta:</strong> <a href={`mailto:${email}`} className="underline">{email}</a></li>
          </ul>
          <div className="flex gap-3">
            <Button asChild variant="gradient">
              <a href={whatsappUrl} target="_blank" rel="noopener noreferrer">WhatsApp</a>
            </Button>
            <Button asChild variant="outline">
              <a href={`tel:${phone}`}>Ara</a>
            </Button>
          </div>
        </div>
        <div className="rounded-2xl p-6 glass-card-modern">
          <h3 className="text-xl font-semibold mb-3">Hızlı Başlangıç</h3>
          <ol className="list-decimal list-inside text-sm text-muted-foreground space-y-2">
            <li>Ücretsiz deneme için formu doldurun</li>
            <li>Size uygun saat için geri dönüş yapalım</li>
            <li>Stüdyoda tanışıp hedeflerinizi planlayalım</li>
          </ol>
        </div>
      </div>
    </section>
  );
}

