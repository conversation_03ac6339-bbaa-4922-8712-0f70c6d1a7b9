"use client";

import { motion } from "framer-motion";
import { Target, Calendar, CheckCircle, Award } from "lucide-react";

interface FlexDashboardStats {
  totalSessions: number;
  usedSessions: number;
  remainingSessions: number;
  scheduledSessions: number;
  completedSessions: number;
  activePackages: number;
}

interface StatsGridProps {
  stats: FlexDashboardStats;
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function StatsGrid({ stats }: StatsGridProps) {
  const statsData = [
    {
      title: "Kalan Sean<PERSON>",
      value: stats.remainingSessions,
      icon: Target,
      gradient: "from-emerald-500/20 to-emerald-500/10",
      iconBg: "bg-emerald-500/20",
      iconColor: "text-emerald-500",
      description: "Bu ay kullanabileceğin",
      priority: 1,
    },
    {
      title: "Planlanmış",
      value: stats.scheduledSessions,
      icon: Calendar,
      gradient: "from-blue-500/20 to-blue-500/10",
      iconBg: "bg-blue-500/20",
      iconColor: "text-blue-500",
      description: "<PERSON><PERSON><PERSON>an randevular",
      priority: 1,
    },
    {
      title: "<PERSON><PERSON>lanm<PERSON><PERSON>",
      value: stats.completedSessions,
      icon: CheckCircle,
      gradient: "from-violet-500/20 to-violet-500/10",
      iconBg: "bg-violet-500/20",
      iconColor: "text-violet-500",
      description: "Toplam başarıların",
      priority: 2,
    },
    {
      title: "Aktif Paket",
      value: stats.activePackages,
      icon: Award,
      gradient: "from-amber-500/20 to-amber-500/10",
      iconBg: "bg-amber-500/20",
      iconColor: "text-amber-500",
      description: "Premium üyelikler",
      priority: 2,
    },
  ];

  // Sort by priority (1 = highest priority)
  const sortedStats = [...statsData].sort((a, b) => a.priority - b.priority);

  return (
    <motion.section
      variants={fadeInUp}
      initial="initial"
      animate="animate"
      className="space-y-4"
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
        {sortedStats.map((stat, index) => (
          <motion.div
            key={stat.title}
            variants={fadeInUp}
            transition={{ delay: index * 0.1 }}
            className="group"
          >
            <div
              className={`relative overflow-hidden rounded-xl p-4 bg-gradient-to-br ${stat.gradient} border border-border/30 backdrop-blur-sm hover:shadow-md transition-all duration-300 h-full`}
            >
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`${stat.iconBg} w-10 h-10 rounded-lg flex items-center justify-center`}
                >
                  <stat.icon className={`w-5 h-5 ${stat.iconColor}`} />
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-3xl font-bold text-foreground group-hover:scale-105 transition-transform origin-left">
                  {stat.value}
                </p>
                <div>
                  <p className="text-sm font-medium text-foreground">
                    {stat.title}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stat.description}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.section>
  );
}
