import React from "react";

interface LotusIconProps {
  className?: string;
  size?: number;
}

export function LotusIcon({ className = "", size = 24 }: LotusIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Lotus petals */}
      <path
        d="M12 2C12 2 8 6 8 10C8 12.21 9.79 14 12 14C14.21 14 16 12.21 16 10C16 6 12 2 12 2Z"
        fill="url(#lotus-gradient-1)"
        opacity="0.9"
      />
      <path
        d="M12 4C12 4 6 7 6 12C6 14.76 8.24 17 11 17C13.76 17 16 14.76 16 12C16 7 12 4 12 4Z"
        fill="url(#lotus-gradient-2)"
        opacity="0.8"
      />
      <path
        d="M12 6C12 6 4 8 4 14C4 17.31 6.69 20 10 20C13.31 20 16 17.31 16 14C16 8 12 6 12 6Z"
        fill="url(#lotus-gradient-3)"
        opacity="0.7"
      />
      <path
        d="M12 4C12 4 18 7 18 12C18 14.76 15.76 17 13 17C10.24 17 8 14.76 8 12C8 7 12 4 12 4Z"
        fill="url(#lotus-gradient-2)"
        opacity="0.8"
      />
      <path
        d="M12 6C12 6 20 8 20 14C20 17.31 17.31 20 14 20C10.69 20 8 17.31 8 14C8 8 12 6 12 6Z"
        fill="url(#lotus-gradient-3)"
        opacity="0.7"
      />
      
      {/* Center */}
      <circle
        cx="12"
        cy="14"
        r="2"
        fill="url(#lotus-center)"
      />
      
      <defs>
        <linearGradient id="lotus-gradient-1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#fdf2f8" />
          <stop offset="50%" stopColor="#fce7f3" />
          <stop offset="100%" stopColor="#fbcfe8" />
        </linearGradient>
        <linearGradient id="lotus-gradient-2" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#fbcfe8" />
          <stop offset="50%" stopColor="#f9a8d4" />
          <stop offset="100%" stopColor="#f472b6" />
        </linearGradient>
        <linearGradient id="lotus-gradient-3" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#f9a8d4" />
          <stop offset="50%" stopColor="#f472b6" />
          <stop offset="100%" stopColor="#ec4899" />
        </linearGradient>
        <radialGradient id="lotus-center">
          <stop offset="0%" stopColor="#be185d" />
          <stop offset="100%" stopColor="#ec4899" />
        </radialGradient>
      </defs>
    </svg>
  );
}

export function LotusSimpleIcon({ className = "", size = 20 }: LotusIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10 2C10 2 7 5 7 8C7 9.66 8.34 11 10 11C11.66 11 13 9.66 13 8C13 5 10 2 10 2Z"
        fill="currentColor"
        opacity="0.9"
      />
      <path
        d="M10 3C10 3 5 5.5 5 9C5 11.21 6.79 13 9 13C11.21 13 13 11.21 13 9C13 5.5 10 3 10 3Z"
        fill="currentColor"
        opacity="0.7"
      />
      <path
        d="M10 3C10 3 15 5.5 15 9C15 11.21 13.21 13 11 13C8.79 13 7 11.21 7 9C7 5.5 10 3 10 3Z"
        fill="currentColor"
        opacity="0.7"
      />
      <path
        d="M10 4C10 4 3 6 3 11C3 13.76 5.24 16 8 16C10.76 16 13 13.76 13 11C13 6 10 4 10 4Z"
        fill="currentColor"
        opacity="0.5"
      />
      <path
        d="M10 4C10 4 17 6 17 11C17 13.76 14.76 16 12 16C9.24 16 7 13.76 7 11C7 6 10 4 10 4Z"
        fill="currentColor"
        opacity="0.5"
      />
      <circle cx="10" cy="11" r="1.5" fill="currentColor" />
    </svg>
  );
}
