"use client";

import { useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { formatDate } from "./appointment-utils";

interface CalendarDatePickerProps {
  selectedDate: string;
  onDateSelect: (date: string) => void;
  bookedDates?: string[];
  originalDate?: string; // For edit mode - to highlight original date
  isEditMode?: boolean;
  minDate?: string; // Minimum selectable date (ISO string)
  maxDate?: string; // Maximum selectable date (ISO string)
}

// Generate week days for a given start date
const generateWeekDays = (startDate: Date) => {
  const days = [];
  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    days.push(date.toISOString().split("T")[0]);
  }
  return days;
};

// CalendarDayButton component
const CalendarDayButton = ({
  day,
  isSelected,
  isToday,
  isBooked,
  isOriginal,
  isEditMode,
  isDisabled,
  onDateSelect,
}: {
  day: string;
  isSelected: boolean;
  isToday: boolean;
  isBooked: boolean;
  isOriginal: boolean;
  isEditMode: boolean;
  isDisabled: boolean;
  onDateSelect: (date: string) => void;
}) => {
  return (
    <button
      key={day}
      disabled={isDisabled}
      className={cn(
        "relative p-3 text-center transition-all duration-200 rounded-lg min-h-[50px] flex flex-col items-center justify-center border",
        // Disabled states
        isDisabled
          ? "cursor-not-allowed opacity-50 border-border/30"
          : "hover:bg-secondary/50 cursor-pointer border-border/50",
        // Selected state
        isSelected && !isDisabled
          ? "bg-primary text-primary-foreground shadow-md border-primary"
          : !isDisabled && "bg-card",
        // Today highlighting
        isToday &&
          !isSelected &&
          !isDisabled &&
          "ring-2 ring-amber-200 dark:ring-amber-800/50 border-amber-400 dark:border-amber-600",
        // Original date highlighting for edit mode
        isOriginal &&
          isEditMode &&
          !isSelected &&
          "ring-2 ring-emerald-200 dark:ring-emerald-800/50 border-emerald-400 dark:border-emerald-600"
      )}
      onClick={() => {
        if (!isDisabled) {
          onDateSelect(day);
        }
      }}
    >
      {isToday && !isDisabled && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-amber-500 rounded-full"></div>
      )}
      {isBooked && !(isEditMode && isOriginal) && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-destructive rounded-full"></div>
      )}
      {isOriginal && isEditMode && (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-emerald-500 rounded-full"></div>
      )}

      <div className="text-xs text-muted-foreground">
        {formatDate(day).split(" ")[0]} {/* Weekday */}
      </div>
      <div className="text-lg font-bold mt-1">{new Date(day).getDate()}</div>
    </button>
  );
};

export default function CalendarDatePicker({
  selectedDate,
  onDateSelect,
  bookedDates = [],
  originalDate,
  isEditMode = false,
  minDate,
  maxDate,
}: CalendarDatePickerProps) {
  const [currentWeekStart, setCurrentWeekStart] = useState(() => {
    // Start with the beginning of the current week (Monday)
    const today = new Date();
    const day = today.getDay();
    // Adjust for Monday as first day (0 = Sunday, 1 = Monday, etc.)
    const diff = today.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(today.setDate(diff));
  });

  const weekDays = useMemo(() => {
    return generateWeekDays(currentWeekStart);
  }, [currentWeekStart]);

  const navigateWeek = (direction: "prev" | "next") => {
    setCurrentWeekStart((prev) => {
      const newDate = new Date(prev);
      newDate.setDate(prev.getDate() + (direction === "prev" ? -7 : 7));
      return newDate;
    });
  };

  // Format week range for display
  const weekRange = useMemo(() => {
    const startDate = new Date(currentWeekStart);
    const endDate = new Date(currentWeekStart);
    endDate.setDate(startDate.getDate() + 6);

    const startStr = startDate.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "long",
    });

    const endStr = endDate.toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "long",
    });

    return `${startStr} - ${endStr}`;
  }, [currentWeekStart]);

  const isDateDisabled = (date: string): boolean => {
    // Check if date is in the past (before today)
    const today = new Date().toISOString().split("T")[0];
    if (date < today) {
      return true;
    }

    // Check if date is booked (except original date in edit mode)
    const isBooked = bookedDates.includes(date);
    const isOriginal = originalDate === date;

    if (isBooked && !(isEditMode && isOriginal)) {
      return true;
    }

    // Check min/max date constraints
    if (minDate && date < minDate) {
      return true;
    }

    if (maxDate && date > maxDate) {
      return true;
    }

    return false;
  };

  // Check if we can navigate to the previous week (prevent going to past weeks)
  const canNavigatePrev = useMemo(() => {
    const prevWeekStart = new Date(currentWeekStart);
    prevWeekStart.setDate(currentWeekStart.getDate() - 7);

    // Get the start of the current week (Monday)
    const today = new Date();
    const day = today.getDay();
    const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Monday as first day
    const currentWeekStartDate = new Date(today.setDate(diff));

    // Allow navigation to previous week only if it's not before the current week
    return prevWeekStart >= currentWeekStartDate;
  }, [currentWeekStart]);

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <div className="w-2 h-2 bg-primary rounded-full"></div>
        <h3 className="text-lg font-semibold text-foreground">Tarih Seçin</h3>
      </div>

      {bookedDates.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800/30 rounded-lg p-4 mb-4">
          <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
            <div className="w-3 h-3 bg-destructive rounded-full"></div>
            <span className="text-sm font-medium">
              {isEditMode
                ? "Kırmızı işaretli tarihler zaten randevunuz olan günlerdir. Mevcut randevunuz hariç diğer tarihleri seçemezsiniz."
                : "Kırmızı işaretli tarihler zaten randevunuz olan günlerdir. Aynı güne birden fazla randevu alamazsınız."}
            </span>
          </div>
        </div>
      )}

      {/* Week Navigation */}
      <div className="flex items-center justify-between">
        <button
          onClick={() => navigateWeek("prev")}
          className="p-2 rounded-lg hover:bg-secondary transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!canNavigatePrev}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </button>

        <h4 className="text-lg font-semibold text-foreground text-center">
          {weekRange}
        </h4>

        <button
          onClick={() => navigateWeek("next")}
          className="p-2 rounded-lg hover:bg-secondary transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {/* Week Days Grid */}
      <div className="grid grid-cols-7 gap-2">
        {/* Weekday Headers */}
        {["Pzt", "Sal", "Çar", "Per", "Cum", "Cmt", "Paz"].map((day) => (
          <div
            key={day}
            className="text-center text-sm font-medium text-muted-foreground py-2"
          >
            {day}
          </div>
        ))}

        {/* Calendar Days */}
        {weekDays.map((day) => {
          const isSelected = selectedDate === day;
          const isToday = day === new Date().toISOString().split("T")[0];
          const isBooked = bookedDates.includes(day);
          const isOriginal = originalDate === day;
          const isDisabled = isDateDisabled(day);

          return (
            <CalendarDayButton
              key={day}
              day={day}
              isSelected={isSelected}
              isToday={isToday}
              isBooked={isBooked}
              isOriginal={isOriginal}
              isEditMode={isEditMode}
              isDisabled={isDisabled}
              onDateSelect={onDateSelect}
            />
          );
        })}
      </div>
    </div>
  );
}
