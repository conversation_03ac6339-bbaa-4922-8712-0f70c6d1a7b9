-- Simplified RLS Policies for Current Phone-Based System
-- Created: 2025-09-08
-- Purpose: Secure access while maintaining compatibility with current phone-based auth system

-- ============================================================================
-- 1. FLEX_PACKAGES TABLE POLICIES
-- ============================================================================

-- Enable RLS for flex_packages
ALTER TABLE flex_packages ENABLE ROW LEVEL SECURITY;

-- Policy 1: Everyone can read flex_packages (public access for reading)
-- This allows unauthenticated users to view available packages
CREATE POLICY "flex_packages_read_public" ON flex_packages
    FOR SELECT
    USING (true);

-- Policy 2: Only authenticated users can create flex_packages
-- Since you're using admin client for operations, this mainly protects against direct API access
CREATE POLICY "flex_packages_create_authenticated" ON flex_packages
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Policy 3: Only authenticated users can update flex_packages
CREATE POLICY "flex_packages_update_authenticated" ON flex_packages
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Policy 4: Only authenticated users can delete flex_packages
CREATE POLICY "flex_packages_delete_authenticated" ON flex_packages
    FOR DELETE
    TO authenticated
    USING (true);

-- ============================================================================
-- 2. MEMBER_FLEX_PACKAGES TABLE POLICIES
-- ============================================================================

-- Enable RLS for member_flex_packages
ALTER TABLE member_flex_packages ENABLE ROW LEVEL SECURITY;

-- Policy 1: Authenticated users can read all member_flex_packages
-- Since you're using admin client, this provides basic protection while allowing flexibility
CREATE POLICY "member_flex_packages_authenticated_read" ON member_flex_packages
    FOR SELECT
    TO authenticated
    USING (true);

-- Policy 2: Authenticated users can create member_flex_packages
CREATE POLICY "member_flex_packages_authenticated_create" ON member_flex_packages
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Policy 3: Authenticated users can update member_flex_packages
CREATE POLICY "member_flex_packages_authenticated_update" ON member_flex_packages
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Policy 4: Authenticated users can delete member_flex_packages
CREATE POLICY "member_flex_packages_authenticated_delete" ON member_flex_packages
    FOR DELETE
    TO authenticated
    USING (true);

-- ============================================================================
-- 3. FLEX_PACKAGE_SESSIONS TABLE POLICIES
-- ============================================================================

-- Enable RLS for flex_package_sessions
ALTER TABLE flex_package_sessions ENABLE ROW LEVEL SECURITY;

-- Policy 1: Authenticated users can read all flex_package_sessions
CREATE POLICY "flex_package_sessions_authenticated_read" ON flex_package_sessions
    FOR SELECT
    TO authenticated
    USING (true);

-- Policy 2: Authenticated users can create flex_package_sessions
CREATE POLICY "flex_package_sessions_authenticated_create" ON flex_package_sessions
    FOR INSERT
    TO authenticated
    WITH CHECK (true);

-- Policy 3: Authenticated users can update flex_package_sessions
CREATE POLICY "flex_package_sessions_authenticated_update" ON flex_package_sessions
    FOR UPDATE
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- Policy 4: Authenticated users can delete flex_package_sessions
CREATE POLICY "flex_package_sessions_authenticated_delete" ON flex_package_sessions
    FOR DELETE
    TO authenticated
    USING (true);

-- ============================================================================
-- 4. FUNCTION-BASED POLICIES (Optional - More Secure Alternative)
-- ============================================================================

/*
If you want to implement more secure policies while keeping your current phone-based system,
you can create custom functions that validate user access:

-- Example: Create a function to check if current user can access member data
CREATE OR REPLACE FUNCTION current_user_can_access_member(member_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    -- Add your custom logic here
    -- For example, you might check session storage, JWT claims, etc.
    -- This is a placeholder - implement according to your needs
    RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Then use it in policies:
CREATE POLICY "member_flex_packages_custom_access" ON member_flex_packages
    FOR ALL
    TO authenticated
    USING (current_user_can_access_member(member_id))
    WITH CHECK (current_user_can_access_member(member_id));
*/

-- ============================================================================
-- 5. IMPORTANT NOTES FOR YOUR CURRENT SYSTEM
-- ============================================================================

/*
IMPORTANT CONSIDERATIONS:

1. ADMIN CLIENT BYPASS:
   - Your current system uses createAdminClient() with service role
   - Service role automatically BYPASSES all RLS policies
   - This means your current application logic will continue to work unchanged

2. SECURITY BENEFITS:
   - These policies protect against direct API access from browser/client
   - Prevents unauthorized access through standard Supabase client
   - Maintains your current server-side admin access pattern

3. PUBLIC READ ACCESS FOR FLEX_PACKAGES:
   - flex_packages has public read access as requested
   - This allows anonymous users to view available packages
   - All other operations require authentication

4. AUTHENTICATION REQUIREMENT:
   - All other tables require authentication for any operation
   - This provides basic security while maintaining flexibility

5. NO BREAKING CHANGES:
   - Your current phone-based system continues to work
   - Admin client operations are unaffected
   - No changes needed to existing application code

6. FUTURE IMPROVEMENTS:
   - You can later add more granular policies
   - Consider implementing proper member linking if needed
   - Monitor access patterns and adjust policies accordingly
*/

-- ============================================================================
-- 6. GRANT PERMISSIONS FOR FUNCTIONS
-- ============================================================================

-- Ensure the auto_complete_past_sessions function can be called by authenticated users
-- (If it's not already accessible)
-- GRANT EXECUTE ON FUNCTION auto_complete_past_sessions() TO authenticated;

-- ============================================================================
-- 7. TESTING QUERIES (Run these to verify policies work)
-- ============================================================================

/*
-- Test flex_packages public read access (should work without authentication)
SELECT * FROM flex_packages WHERE is_active = true;

-- Test with regular authenticated user (should work)
-- These require authentication but your admin client bypasses RLS anyway

-- Test member_flex_packages access
SELECT * FROM member_flex_packages LIMIT 5;

-- Test flex_package_sessions access  
SELECT * FROM flex_package_sessions LIMIT 5;
*/