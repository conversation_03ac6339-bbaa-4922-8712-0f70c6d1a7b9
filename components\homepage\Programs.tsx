import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Activity, HeartPulse, Waves, Zap } from "lucide-react";

const programs = [
  { title: "Pilates (Grup)", desc: "Esneklik ve core gücü", Icon: HeartPulse },
  { title: "Yoga (Grup)", desc: "Denge ve nefes", Icon: Waves },
  { title: "Zumba (Grup)", desc: "Ritim ve kardiyo", Icon: Activity },
  { title: "HIIT (Grup)", desc: "<PERSON><PERSON><PERSON> sürede yoğun verim", Icon: Zap },
];

export default function Programs() {
  return (
    <section id="programlar" className="section-padding">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">Programlar</h2>
          <p className="text-muted-foreground mt-2">Tüm seviyelere uygun ders seçenekleri</p>
        </div>
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {programs.map(({ title, desc, Icon }) => (
            <Card key={title} className="glass-card-service hover-lift">
              <CardHeader className="flex-row items-center gap-3">
                <div className="size-10 rounded-xl bg-gradient-primary grid place-items-center text-white">
                  <Icon className="size-5" />
                </div>
                <CardTitle className="text-lg">{title}</CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                <CardDescription>{desc}</CardDescription>
              </CardContent>
              <div className="px-6 pb-6">
                <Button asChild variant="soft" className="w-full">
                  <Link href="/appointments/new">Ücretsiz Deneme</Link>
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

