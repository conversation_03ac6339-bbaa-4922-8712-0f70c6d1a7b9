-- RLS Policies for Flex Package System Tables
-- Created: 2025-09-08
-- Purpose: Secure access to flex_packages, member_flex_packages, and flex_package_sessions tables

-- ============================================================================
-- 1. FLEX_PACKAGES TABLE POLICIES
-- ============================================================================

-- Enable RLS for flex_packages
ALTER TABLE flex_packages ENABLE ROW LEVEL SECURITY;

-- Policy 1: Everyone can read flex_packages (public access for reading)
CREATE POLICY "flex_packages_read_public" ON flex_packages
    FOR SELECT
    USING (true);

-- Policy 2: Only authenticated users can create flex_packages
CREATE POLICY "flex_packages_create_authenticated" ON flex_packages
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

-- Policy 3: Only authenticated users can update flex_packages
CREATE POLICY "flex_packages_update_authenticated" ON flex_packages
    FOR UPDATE
    USING (auth.role() = 'authenticated')
    WITH CHECK (auth.role() = 'authenticated');

-- Policy 4: Only authenticated users can delete flex_packages
CREATE POLICY "flex_packages_delete_authenticated" ON flex_packages
    FOR DELETE
    USING (auth.role() = 'authenticated');

-- ============================================================================
-- 2. MEMBER_FLEX_PACKAGES TABLE POLICIES
-- ============================================================================

-- Enable RLS for member_flex_packages
ALTER TABLE member_flex_packages ENABLE ROW LEVEL SECURITY;

-- Policy 1: Users can only read their own packages (if they have a corresponding member record)
CREATE POLICY "member_flex_packages_read_own" ON member_flex_packages
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = member_flex_packages.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 2: Users can only create packages for themselves (if they have a corresponding member record)
CREATE POLICY "member_flex_packages_create_own" ON member_flex_packages
    FOR INSERT
    WITH CHECK (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = member_flex_packages.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 3: Users can only update their own packages
CREATE POLICY "member_flex_packages_update_own" ON member_flex_packages
    FOR UPDATE
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = member_flex_packages.member_id
            AND members.auth_user_id = auth.uid()
        )
    )
    WITH CHECK (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = member_flex_packages.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 4: Users can only delete their own packages
CREATE POLICY "member_flex_packages_delete_own" ON member_flex_packages
    FOR DELETE
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = member_flex_packages.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- ============================================================================
-- 3. FLEX_PACKAGE_SESSIONS TABLE POLICIES
-- ============================================================================

-- Enable RLS for flex_package_sessions
ALTER TABLE flex_package_sessions ENABLE ROW LEVEL SECURITY;

-- Policy 1: Users can only read their own sessions
CREATE POLICY "flex_package_sessions_read_own" ON flex_package_sessions
    FOR SELECT
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = flex_package_sessions.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 2: Users can only create sessions for themselves
CREATE POLICY "flex_package_sessions_create_own" ON flex_package_sessions
    FOR INSERT
    WITH CHECK (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = flex_package_sessions.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 3: Users can only update their own sessions
CREATE POLICY "flex_package_sessions_update_own" ON flex_package_sessions
    FOR UPDATE
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = flex_package_sessions.member_id
            AND members.auth_user_id = auth.uid()
        )
    )
    WITH CHECK (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = flex_package_sessions.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- Policy 4: Users can only delete their own sessions
CREATE POLICY "flex_package_sessions_delete_own" ON flex_package_sessions
    FOR DELETE
    USING (
        auth.role() = 'authenticated' AND
        EXISTS (
            SELECT 1 FROM members
            WHERE members.id = flex_package_sessions.member_id
            AND members.auth_user_id = auth.uid()
        )
    );

-- ============================================================================
-- 4. ADMIN ACCESS POLICIES (Optional - for administrative operations)
-- ============================================================================

-- If you have admin users, you can create additional policies
-- Example: Admin users can access all records regardless of ownership

-- Admin can read all member_flex_packages
-- CREATE POLICY "member_flex_packages_admin_read" ON member_flex_packages
--     FOR SELECT
--     USING (
--         auth.role() = 'authenticated' AND
--         EXISTS (
--             SELECT 1 FROM members
--             WHERE members.auth_user_id = auth.uid()
--             AND members.membership_type = 'admin'  -- Assuming you have an admin type
--         )
--     );

-- Admin can manage all flex_package_sessions
-- CREATE POLICY "flex_package_sessions_admin_all" ON flex_package_sessions
--     FOR ALL
--     USING (
--         auth.role() = 'authenticated' AND
--         EXISTS (
--             SELECT 1 FROM members
--             WHERE members.auth_user_id = auth.uid()
--             AND members.membership_type = 'admin'
--         )
--     )
--     WITH CHECK (
--         auth.role() = 'authenticated' AND
--         EXISTS (
--             SELECT 1 FROM members
--             WHERE members.auth_user_id = auth.uid()
--             AND members.membership_type = 'admin'
--         )
--     );

-- ============================================================================
-- 5. ADDITIONAL SECURITY NOTES
-- ============================================================================

/*
IMPORTANT SECURITY CONSIDERATIONS:

1. The policies assume that the 'members' table has an 'auth_user_id' column 
   that links to Supabase Auth users. If this doesn't exist, you'll need to:
   - Add the column: ALTER TABLE members ADD COLUMN auth_user_id UUID REFERENCES auth.users(id);
   - Update existing records to link members to their auth users

2. For the current system that uses phone-based authentication without traditional auth:
   - You might want to create a different policy structure
   - Consider creating a custom authentication function
   - Or modify policies to work with your current phone-based system

3. If you're using service role for admin operations (like in your current code):
   - Service role bypasses RLS automatically
   - No need for special admin policies if using service role

4. Test these policies thoroughly in a development environment before applying to production

5. Monitor query performance as these policies add joins to the members table
*/

-- ============================================================================
-- 6. ALTERNATIVE POLICIES FOR PHONE-BASED SYSTEM (if needed)
-- ============================================================================

/*
If you want to continue with phone-based authentication without traditional Supabase Auth,
you could create simpler policies that just require authentication:

-- Simple authenticated-only policies (less secure but simpler)
DROP POLICY IF EXISTS "member_flex_packages_read_own" ON member_flex_packages;
CREATE POLICY "member_flex_packages_authenticated_only" ON member_flex_packages
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);

-- This would allow any authenticated user to access any record
-- Only use this if you handle authorization in your application layer
*/