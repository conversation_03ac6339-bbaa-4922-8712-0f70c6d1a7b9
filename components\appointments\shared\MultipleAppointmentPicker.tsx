"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Plus, AlertCircle, Trash2 } from "lucide-react";
import { formatDate } from "./appointment-utils";
import DatePicker from "./DatePicker";
import TimeSlotPicker from "./TimeSlotPicker";
import type { TimeSlotCapacityMap } from "./appointment-utils";

interface SelectedAppointment {
  date: string;
  time: string;
}

interface MultipleAppointmentPickerProps {
  selectedAppointments: SelectedAppointment[];
  onAppointmentsChange: (appointments: SelectedAppointment[]) => void;
  bookedDates: string[];
  timeSlotCapacity: TimeSlotCapacityMap;
  loadingTimeSlots: boolean;
  fetchTimeSlotCapacities: (date: string) => void;
  maxAppointments?: number;
}

export default function MultipleAppointmentPicker({
  selectedAppointments,
  onAppointmentsChange,
  bookedDates,
  timeSlotCapacity,
  loadingTimeSlots,
  fetchTimeSlotCapacities,
  maxAppointments = 12,
}: MultipleAppointmentPickerProps) {
  const [currentDate, setCurrentDate] = useState("");
  const [currentTime, setCurrentTime] = useState("");

  // Tarih seçildiğinde kapasiteleri getir
  const handleDateSelection = (date: string) => {
    setCurrentDate(date);
    setCurrentTime(""); // Saat seçimini sıfırla
    fetchTimeSlotCapacities(date);
  };

  // Randevu ekleme
  const addAppointment = () => {
    if (!currentDate || !currentTime) {
      return;
    }

    // Aynı tarih kontrolü (spor salonu için)
    const hasSameDate = selectedAppointments.some(
      (apt) => apt.date === currentDate
    );

    if (hasSameDate) {
      return;
    }

    // Aynı tarih ve saat kontrolü
    const isDuplicate = selectedAppointments.some(
      (apt) => apt.date === currentDate && apt.time === currentTime
    );

    if (isDuplicate) {
      return;
    }

    // Maksimum randevu sayısı kontrolü
    if (selectedAppointments.length >= maxAppointments) {
      return;
    }

    const newAppointment = { date: currentDate, time: currentTime };
    const updatedAppointments = [...selectedAppointments, newAppointment];

    // Tarihe göre sırala
    updatedAppointments.sort((a, b) => {
      if (a.date === b.date) {
        return a.time.localeCompare(b.time);
      }
      return a.date.localeCompare(b.date);
    });

    onAppointmentsChange(updatedAppointments);

    // Formu temizle
    setCurrentDate("");
    setCurrentTime("");
  };

  // Randevu silme
  const removeAppointment = (index: number) => {
    const updatedAppointments = selectedAppointments.filter(
      (_, i) => i !== index
    );
    onAppointmentsChange(updatedAppointments);
  };

  // Seçilen tarih ve saatlerin kontrolü
  const isTimeSlotSelected = (date: string, time: string) => {
    return selectedAppointments.some(
      (apt) => apt.date === date && apt.time === time
    );
  };

  // Aynı tarih kontrolü
  const isDateSelected = (date: string) => {
    return selectedAppointments.some((apt) => apt.date === date);
  };

  const canAddAppointment =
    currentDate &&
    currentTime &&
    !isTimeSlotSelected(currentDate, currentTime) &&
    !isDateSelected(currentDate) &&
    selectedAppointments.length < maxAppointments;

  return (
    <div className="space-y-6">
      {/* Seçilen Randevular */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Calendar className="w-5 h-5 text-primary" />
              </div>
              <div>
                <span className="text-lg text-foreground">
                  Seçilen Randevular
                </span>
                <p className="text-sm text-muted-foreground font-normal">
                  {selectedAppointments.length}/{maxAppointments} randevu
                  seçildi
                </p>
              </div>
            </div>
            <Badge variant="secondary">
              {selectedAppointments.length} randevu
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {selectedAppointments.length === 0 ? (
            <div className="text-center py-8 space-y-4">
              <Calendar className="w-12 h-12 mx-auto text-muted-foreground/40" />
              <div>
                <p className="font-medium text-foreground">
                  Henüz randevu seçilmedi
                </p>
                <p className="text-muted-foreground text-sm">
                  Aşağıdan randevu ekleyerek başlayın
                </p>
              </div>
            </div>
          ) : (
            <div className="grid gap-3">
              {selectedAppointments.map((appointment, index) => (
                <div
                  key={`${appointment.date}-${appointment.time}`}
                  className="flex items-center justify-between p-4 border rounded-lg bg-secondary/30"
                >
                  <div className="flex items-center space-x-4">
                    <div className="p-2 bg-primary/10 rounded-lg">
                      <Clock className="w-4 h-4 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium text-foreground">
                        {formatDate(appointment.date)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {appointment.time}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAppointment(index)}
                    className="text-destructive hover:text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Randevu Ekleme Formu */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Plus className="w-5 h-5 text-primary" />
              </div>
              <span className="text-lg text-foreground">Yeni Randevu Ekle</span>
            </div>
            {selectedAppointments.length >= maxAppointments && (
              <Badge variant="destructive">Maksimum limit</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {selectedAppointments.length >= maxAppointments ? (
            <div className="text-center py-8 space-y-4">
              <AlertCircle className="w-12 h-12 mx-auto text-orange-500" />
              <div>
                <p className="font-medium text-foreground">
                  Maksimum randevu sayısına ulaştınız
                </p>
                <p className="text-muted-foreground text-sm">
                  En fazla {maxAppointments} randevu seçebilirsiniz
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Tarih Seçimi */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <h3 className="text-lg font-semibold text-foreground">
                    Tarih Seçin
                  </h3>
                </div>
                <DatePicker
                  selectedDate={currentDate}
                  onDateSelect={handleDateSelection}
                  bookedDates={[
                    ...bookedDates,
                    ...selectedAppointments.map((apt) => apt.date),
                  ]}
                />
              </div>

              {/* Saat Seçimi */}
              {currentDate && (
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                    <h3 className="text-lg font-semibold text-foreground">
                      Saat Seçin
                    </h3>
                  </div>
                  <TimeSlotPicker
                    selectedDate={currentDate}
                    selectedTime={currentTime}
                    onTimeSelect={setCurrentTime}
                    timeSlotCapacity={timeSlotCapacity}
                    loadingTimeSlots={loadingTimeSlots}
                  />
                </div>
              )}

              {/* Ekleme Butonu */}
              {currentDate && currentTime && (
                <div className="space-y-3 pt-4">
                  {isDateSelected(currentDate) && (
                    <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                      <div className="flex items-center gap-2 text-orange-700">
                        <AlertCircle className="w-4 h-4" />
                        <span className="text-sm font-medium">
                          Bu tarihte zaten randevunuz var. Aynı güne birden
                          fazla randevu alamazsınız.
                        </span>
                      </div>
                    </div>
                  )}
                  <div className="flex justify-end">
                    <Button
                      onClick={addAppointment}
                      disabled={!canAddAppointment}
                      className="flex items-center gap-2"
                    >
                      <Plus className="w-4 h-4" />
                      Randevu Ekle
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
