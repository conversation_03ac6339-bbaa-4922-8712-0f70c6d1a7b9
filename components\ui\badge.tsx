import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-all focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 shadow-xs",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/90",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "text-foreground border-input",
        gradient: "bg-gradient-primary text-white border-transparent",
        soft: "bg-primary/10 text-primary border border-primary/20",
        glass:
          "bg-white/60 dark:bg-white/10 backdrop-blur-md border-white/40 dark:border-white/10 text-foreground",
      },
      shape: {
        rounded: "rounded-md",
        pill: "rounded-full px-3 py-1",
      },
      size: {
        sm: "text-[10px] px-2 py-0.5",
        md: "text-xs px-2.5 py-0.5",
        lg: "text-sm px-3 py-1",
      },
    },
    defaultVariants: {
      variant: "default",
      shape: "rounded",
      size: "md",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, shape, size, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, shape, size }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
