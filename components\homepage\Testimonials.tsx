import { Card, CardContent } from "@/components/ui/card";
import { Star } from "lucide-react";

const testimonials = [
  { name: "<PERSON><PERSON>", content: "<PERSON><PERSON><PERSON> güvende ve motive hissediyorum. Eğitmenler harika!" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", content: "<PERSON><PERSON> esnek, stüdyo tertemiz. Çok memnunum." },
  { name: "<PERSON><PERSON><PERSON><PERSON>", content: "<PERSON><PERSON>sa sürede gözle görülür gelişme yaşadım." },
];

export default function Testimonials() {
  return (
    <section id="yorumlar" className="section-padding bg-gradient-brand-subtle">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight">Üyelerimiz Ne Diyor?</h2>
          <p className="text-muted-foreground mt-2">Topluluğumuzdan kısa yorumlar</p>
        </div>
        <div className="grid md:grid-cols-3 gap-6">
          {testimonials.map((t) => (
            <Card key={t.name} className="glass-card-modern">
              <CardContent className="pt-6 space-y-3">
                <div className="flex gap-1 text-yellow-500" aria-hidden>
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star key={i} className="size-4 fill-current" />
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">“{t.content}”</p>
                <div className="text-sm font-medium">{t.name}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}

