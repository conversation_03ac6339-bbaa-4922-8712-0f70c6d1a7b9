-- Flex Package Sessions Table
-- Bu tablo esnek paketlerden kullanılan seansları takip eder
-- Basit 2 status: scheduled (planlandı), completed (tamamlandı)
-- <PERSON><PERSON><PERSON> alındığında direkt used_sessions artar
-- 12 saat öncesine kadar iptal/değişiklik mümkün

CREATE TABLE flex_package_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP DEFAULT NOW(),
  
  -- Relationships
  member_id UUID NOT NULL REFERENCES members(id) ON DELETE CASCADE,
  member_package_id UUID NOT NULL REFERENCES member_flex_packages(id) ON DELETE SET NULL,
  
  -- Session Details
  session_date DATE NOT NULL,
  session_time TIME NOT NULL,
  
  -- Status Tracking (Simplified)
  status VARCHAR(20) CHECK (status IN ('scheduled', 'completed') OR status IS NULL) DEFAULT 'scheduled',
  
  -- Business Rules (removed generated column - will use VIEW instead)
  -- is_locked will be calculated in flex_sessions_live VIEW
  
  UNIQUE(member_package_id, session_date, session_time),
  

  CONSTRAINT future_session_date CHECK (
    session_date >= CURRENT_DATE
  )
);

-- Indexes
CREATE INDEX idx_flex_sessions_member_id ON flex_package_sessions(member_id);
CREATE INDEX idx_flex_sessions_package_id ON flex_package_sessions(member_package_id);
CREATE INDEX idx_flex_sessions_date ON flex_package_sessions(session_date);
CREATE INDEX idx_flex_sessions_status ON flex_package_sessions(status);
CREATE INDEX idx_flex_sessions_scheduled ON flex_package_sessions(session_date, session_time) WHERE status = 'scheduled';
-- Note: is_locked is computed in flex_sessions_live VIEW, not available as base table column

-- Trigger to update used_sessions count 
CREATE OR REPLACE FUNCTION update_used_sessions_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Yeni randevu alındığında direkt used_sessions artar
    UPDATE member_flex_packages 
    SET used_sessions = used_sessions + 1,
        updated_at = NOW()
    WHERE id = NEW.member_package_id;
    
  ELSIF TG_OP = 'DELETE' THEN
    -- Randevu silindiğinde used_sessions azalır
    UPDATE member_flex_packages 
    SET used_sessions = GREATEST(used_sessions - 1, 0),
        updated_at = NOW()
    WHERE id = OLD.member_package_id;
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER trigger_update_used_sessions_count
  AFTER INSERT OR DELETE ON flex_package_sessions
  FOR EACH ROW
  EXECUTE FUNCTION update_used_sessions_count();

-- Auto-complete past sessions function
-- Frontend'den çağrılabilir: await supabase.rpc('auto_complete_past_sessions')
CREATE OR REPLACE FUNCTION auto_complete_past_sessions()
RETURNS INTEGER AS $$
DECLARE
  updated_count INTEGER;
  current_timestamp TIMESTAMP;
BEGIN
  -- Şu anki zamanı al (timezone aware)
  current_timestamp := NOW();

  -- Saati geçmiş tüm scheduled randevuları completed yap
  -- session_date ve session_time'ı birleştirip TIMESTAMP oluştur
  UPDATE flex_package_sessions
  SET status = 'completed',
      updated_at = current_timestamp
  WHERE status = 'scheduled'
    AND (
      -- Tarih bugünden önceyse kesinlikle geçmiş
      session_date < CURRENT_DATE
      OR
      -- Tarih bugünse ve saat geçmişse
      (session_date = CURRENT_DATE AND
       (session_date::TIMESTAMP + session_time::INTERVAL) < current_timestamp)
    );

  GET DIAGNOSTICS updated_count = ROW_COUNT;

  -- Debug için log yazdır
  RAISE NOTICE 'Auto-completed % past sessions at %. Current time: %',
    updated_count, current_timestamp, current_timestamp;

  RETURN updated_count;
END;
$$ LANGUAGE plpgsql;