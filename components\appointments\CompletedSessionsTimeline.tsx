"use client";

import { motion } from "framer-motion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { TrendingUp, History } from "lucide-react";
import SessionItem from "./SessionItem";
import type { Member } from "@/lib/supabase/types";
import Link from "next/link";

interface ServerFlexPackageSessionWithDetails {
  id: string;
  member_id: string;
  member_package_id: string;
  session_date: string;
  session_time: string;
  status: "scheduled" | "completed";
  notes?: string | null;
  created_at: string;
  updated_at: string;
  member_package?: {
    flex_package?: {
      id: string;
      name: string;
      description: string | null;
      duration_days: number;
      session_count: number;
      price: number;
      is_active: boolean;
    } | null;
  } | null;
  member?: Member | null;
  packageName?: string;
}

interface CompletedSessionsTimelineProps {
  sessions: ServerFlexPackageSessionWithDetails[];
}

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
};

export default function CompletedSessionsTimeline({
  sessions,
}: CompletedSessionsTimelineProps) {
  return (
    <motion.section variants={fadeInUp} className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-xl bg-emerald-500/10 flex items-center justify-center">
            <History className="w-6 h-6 text-emerald-500" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-foreground">
              Geçmiş Performansınız
            </h2>
            <p className="text-muted-foreground">
              Tamamladığınız antrenmanlar ve ilerlemeniz
            </p>
          </div>
        </div>
        <Badge variant="outline" className="px-3 py-1.5 text-sm">
          {sessions.length} Tamamlanmış Seans
        </Badge>
      </div>

      <div className="bg-gradient-to-br from-card to-secondary/5 border border-border/30 rounded-xl shadow-md overflow-hidden backdrop-blur-sm">
        <div className="p-4">
          {sessions.length === 0 ? (
            <div className="text-center py-8">
              <History className="w-10 h-10 mx-auto text-muted-foreground/40 mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                Henüz Tamamlanmış Seansınız Yok
              </h3>
              <p className="text-muted-foreground mb-5">
                İlk antrenmanınızı tamamladığınızda burada görünecek
              </p>
              <Button variant="outline" size="sm" asChild>
                <Link href="/appointments/new">İlk Randevunuzu Alın</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-3">
              {sessions.slice(0, 5).map((session, index) => (
                <SessionItem
                  key={session.id}
                  session={session}
                  isHistorical={true}
                  index={index}
                />
              ))}
              {sessions.length > 5 && (
                <div className="text-center pt-5 border-t border-border/30">
                  <Button variant="outline" size="sm">
                    <TrendingUp className="w-5 h-5 mr-2" />
                    Tüm Geçmişi Gör ({sessions.length - 5} daha)
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.section>
  );
}
