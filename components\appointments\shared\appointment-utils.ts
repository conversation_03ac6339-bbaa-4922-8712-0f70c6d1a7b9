// Shared utility functions for appointment-related components

// Working hours configuration
export const WORKING_HOURS = [
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
  "18:00",
  "19:00",
  "20:00",
];

// Date formatting utilities
export const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("tr-TR", {
    day: "numeric",
    month: "long",
    weekday: "short",
  });
};

export const formatTime = (timeString: string) => {
  return timeString.slice(0, 5); // Remove seconds if present
};

// Generate next seven days
export const getNextSevenDays = () => {
  const days = [];
  const today = new Date();
  for (let i = 0; i < 7; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);
    days.push(date.toISOString().split("T")[0]);
  }
  return days;
};

// Check if time slot is in the past
export const isTimeSlotInPast = (date: string, time: string): boolean => {
  const now = new Date();
  const slotDateTime = new Date(`${date}T${time}`);
  return slotDateTime < now;
};

// Time slot capacity type
export interface TimeSlotCapacity {
  currentCount: number;
  maxCapacity: number;
  available: boolean;
}

export type TimeSlotCapacityMap = Record<string, TimeSlotCapacity>;
