@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);
}

/* LocaFitStudio Modern Design System */

/* Feminine Lotus-Inspired Gradients */
.bg-gradient-modern {
  background: radial-gradient(
      circle at 20% 80%,
      rgba(236, 72, 153, 0.12) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(244, 114, 182, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(251, 207, 232, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 60% 10%,
      rgba(219, 39, 119, 0.08) 0%,
      transparent 50%
    ),
    linear-gradient(135deg, #fdf2f8 0%, #fce7f3 50%, #fbcfe8 100%);
}

.dark .bg-gradient-modern {
  background: radial-gradient(
      circle at 20% 80%,
      rgba(236, 72, 153, 0.08) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(244, 114, 182, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(251, 207, 232, 0.06) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 60% 10%,
      rgba(219, 39, 119, 0.05) 0%,
      transparent 50%
    ),
    linear-gradient(135deg, #0f0a0d 0%, #1a0f15 50%, #2d1b2e 100%);
}

/* Additional semantic colors for appointments */
.bg-success {
  background-color: hsl(var(--chart-5));
}
.text-success {
  color: hsl(var(--chart-5));
}
.border-success {
  border-color: hsl(var(--chart-5));
}
.bg-success-foreground {
  background-color: hsl(var(--background));
}
.text-success-foreground {
  color: hsl(var(--foreground));
}

.bg-warning {
  background-color: hsl(var(--chart-4));
}
.text-warning {
  color: hsl(var(--chart-4));
}
.border-warning {
  border-color: hsl(var(--chart-4));
}
.bg-warning-foreground {
  background-color: hsl(var(--background));
}
.text-warning-foreground {
  color: hsl(var(--foreground));
}

.bg-info {
  background-color: hsl(var(--chart-2));
}
.text-info {
  color: hsl(var(--chart-2));
}
.border-info {
  border-color: hsl(var(--chart-2));
}
.bg-info-foreground {
  background-color: hsl(var(--background));
}
.text-info-foreground {
  color: hsl(var(--foreground));
}

.bg-brand {
  background-color: hsl(var(--chart-1));
}
.text-brand {
  color: hsl(var(--chart-1));
}
.border-brand {
  border-color: hsl(var(--chart-1));
}
.bg-brand-foreground {
  background-color: hsl(var(--background));
}
.text-brand-foreground {
  color: hsl(var(--foreground));
}

/* Gradient backgrounds using theme colors */
.bg-gradient-brand {
  background: linear-gradient(
    to right,
    hsl(var(--chart-1)),
    hsl(var(--chart-4))
  );
}

.bg-gradient-brand-subtle {
  background: linear-gradient(
    to right,
    hsl(var(--chart-1) / 0.1),
    hsl(var(--chart-4) / 0.1)
  );
}

.bg-gradient-success {
  background: linear-gradient(
    to right,
    hsl(var(--chart-5)),
    hsl(var(--chart-2))
  );
}

.bg-gradient-info {
  background: linear-gradient(
    to right,
    hsl(var(--chart-2)),
    hsl(var(--chart-3))
  );
}

/* Status colors for appointments */
.bg-status-scheduled {
  background-color: hsl(var(--chart-2));
}
.text-status-scheduled {
  color: hsl(var(--chart-2));
}
.border-status-scheduled {
  border-color: hsl(var(--chart-2));
}

.bg-status-completed {
  background-color: hsl(var(--chart-5));
}
.text-status-completed {
  color: hsl(var(--chart-5));
}
.border-status-completed {
  border-color: hsl(var(--chart-5));
}

.bg-status-cancelled {
  background-color: hsl(var(--destructive));
}
.text-status-cancelled {
  color: hsl(var(--destructive));
}
.border-status-cancelled {
  border-color: hsl(var(--destructive));
}

/* Modern Typography */
.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 900;
  line-height: 0.9;
  letter-spacing: -0.04em;
  color: #1a1a1a;
}

.dark .hero-title {
  color: #ffffff;
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  font-weight: 400;
  line-height: 1.6;
  color: #6b7280;
}

.dark .hero-subtitle {
  color: #9ca3af;
}

.modern-gradient-text {
  background: linear-gradient(135deg, #ec4899 0%, #f472b6 30%, #f9a8d4 60%, #fbcfe8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-modern {
  background: linear-gradient(135deg, #be185d 0%, #ec4899 30%, #f472b6 60%, #f9a8d4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 900;
}

/* Modern Glass Morphism */
.glass-card-modern {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.dark .glass-card-modern {
  background: rgba(15, 15, 15, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card-hero {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.dark .glass-card-hero {
  background: rgba(10, 10, 10, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.glass-card-service {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.25);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .glass-card-service {
  background: rgba(20, 20, 20, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-card-service:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.dark .glass-card-service:hover {
  background: rgba(20, 20, 20, 0.8);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Modern Buttons */
.modern-btn-primary {
  background: linear-gradient(135deg, #ec4899 0%, #f472b6 50%, #f9a8d4 100%);
  color: white;
  font-weight: 600;
  font-size: 1.125rem;
  padding: 1rem 2rem;
  border-radius: 1rem;
  border: none;
  box-shadow: 0 10px 30px rgba(236, 72, 153, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.modern-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(236, 72, 153, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.modern-btn-primary:active {
  transform: translateY(0);
}

.modern-btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  color: #374151;
  font-weight: 500;
  font-size: 1.125rem;
  padding: 1rem 2rem;
  border-radius: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .modern-btn-secondary {
  background: rgba(30, 30, 30, 0.8);
  color: #e5e7eb;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .modern-btn-secondary:hover {
  background: rgba(40, 40, 40, 0.95);
}

/* Feminine Lotus Badge */
.modern-badge {
  background: rgba(253, 242, 248, 0.9);
  backdrop-filter: blur(10px);
  color: #be185d;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 1.25rem;
  border-radius: 2rem;
  border: 1px solid rgba(236, 72, 153, 0.2);
  box-shadow: 0 4px 15px rgba(236, 72, 153, 0.1);
}

.dark .modern-badge {
  background: rgba(45, 27, 46, 0.9);
  color: #f9a8d4;
  border: 1px solid rgba(249, 168, 212, 0.2);
}

/* Modern Service Cards */
.service-card-modern {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dark .service-card-modern {
  background: rgba(15, 15, 15, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.service-card-modern:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.dark .service-card-modern:hover {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.service-card-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.8),
    transparent
  );
}

/* Modern Animations */
@keyframes float-modern {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-15px) rotate(2deg);
  }
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(236, 72, 153, 0.6);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-float-modern {
  animation: float-modern 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Legacy gradient text support */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Legacy gradient background */
.bg-gradient-primary {
  background: linear-gradient(135deg, #ec4899 0%, #f472b6 50%, #f9a8d4 100%);
}

.bg-gradient-lotus {
  background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 30%, #fbcfe8 60%, #f9a8d4 100%);
}

.bg-gradient-feminine {
  background: linear-gradient(45deg, #ec4899 0%, #f472b6 25%, #f9a8d4 50%, #fbcfe8 75%, #fdf2f8 100%);
}

/* Legacy button styles */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
  transform: scale(1.05);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(236, 72, 153, 0.25);
}

.section-padding {
  padding: 5rem 0;
}

@media (max-width: 768px) {
  .section-padding {
    padding: 3rem 0;
  }
}

.shadow-soft {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.cta-arrow {
  animation: arrowMove 2s ease-in-out infinite;
}

@keyframes arrowMove {
  0%,
  100% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(5px);
  }
}
@media (max-width: 768px) {
  .hero-title {
    font-size: clamp(2.5rem, 10vw, 4rem);
    line-height: 0.95;
  }

  .hero-subtitle {
    font-size: clamp(1rem, 4vw, 1.25rem);
  }

  .modern-btn-primary,
  .modern-btn-secondary {
    font-size: 1rem;
    padding: 0.875rem 1.5rem;
  }

  .glass-card-modern {
    padding: 1.5rem;
  }

  .service-card-modern {
    padding: 1.5rem;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@theme inline {
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-mono: JetBrains Mono, monospace;
  --font-serif: Source Serif 4, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-foreground: var(--foreground);
  --color-background: var(--background);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: oklch(0.9824 0.0013 286.3757);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.3211 0 0);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.3211 0 0);
  --primary: oklch(0.6487 0.1538 150.3071);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6746 0.1414 261.3380);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.8828 0.0285 98.1033);
  --muted-foreground: oklch(0.5382 0 0);
  --accent: oklch(0.8269 0.1080 211.9627);
  --accent-foreground: oklch(0.3211 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8699 0 0);
  --input: oklch(0.8699 0 0);
  --ring: oklch(0.6487 0.1538 150.3071);
  --chart-1: oklch(0.6487 0.1538 150.3071);
  --chart-2: oklch(0.6746 0.1414 261.3380);
  --chart-3: oklch(0.8269 0.1080 211.9627);
  --chart-4: oklch(0.5880 0.0993 245.7394);
  --chart-5: oklch(0.5905 0.1608 148.2409);
  --radius: 0.5rem;
  --sidebar: oklch(0.9824 0.0013 286.3757);
  --sidebar-foreground: oklch(0.3211 0 0);
  --sidebar-primary: oklch(0.6487 0.1538 150.3071);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.8269 0.1080 211.9627);
  --sidebar-accent-foreground: oklch(0.3211 0 0);
  --sidebar-border: oklch(0.8699 0 0);
  --sidebar-ring: oklch(0.6487 0.1538 150.3071);
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
}

.dark {
  --background: oklch(0.2303 0.0125 264.2926);
  --foreground: oklch(0.9219 0 0);
  --card: oklch(0.3210 0.0078 223.6661);
  --card-foreground: oklch(0.9219 0 0);
  --popover: oklch(0.3210 0.0078 223.6661);
  --popover-foreground: oklch(0.9219 0 0);
  --primary: oklch(0.6487 0.1538 150.3071);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.5880 0.0993 245.7394);
  --secondary-foreground: oklch(0.9219 0 0);
  --muted: oklch(0.3867 0 0);
  --muted-foreground: oklch(0.7155 0 0);
  --accent: oklch(0.6746 0.1414 261.3380);
  --accent-foreground: oklch(0.9219 0 0);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3867 0 0);
  --input: oklch(0.3867 0 0);
  --ring: oklch(0.6487 0.1538 150.3071);
  --chart-1: oklch(0.6487 0.1538 150.3071);
  --chart-2: oklch(0.5880 0.0993 245.7394);
  --chart-3: oklch(0.6746 0.1414 261.3380);
  --chart-4: oklch(0.8269 0.1080 211.9627);
  --chart-5: oklch(0.5905 0.1608 148.2409);
  --radius: 0.5rem;
  --sidebar: oklch(0.2303 0.0125 264.2926);
  --sidebar-foreground: oklch(0.9219 0 0);
  --sidebar-primary: oklch(0.6487 0.1538 150.3071);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.6746 0.1414 261.3380);
  --sidebar-accent-foreground: oklch(0.9219 0 0);
  --sidebar-border: oklch(0.3867 0 0);
  --sidebar-ring: oklch(0.6487 0.1538 150.3071);
  --font-sans: Plus Jakarta Sans, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --shadow-color: oklch(0 0 0);
  --shadow-opacity: 0.1;
  --shadow-blur: 3px;
  --shadow-spread: 0px;
  --shadow-offset-x: 0;
  --shadow-offset-y: 1px;
  --letter-spacing: 0em;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}