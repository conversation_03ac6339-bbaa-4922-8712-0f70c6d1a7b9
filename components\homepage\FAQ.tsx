export default function FAQ() {
  const faqs = [
    { q: "Ücretsiz deneme dersi nasıl çalışıyor?", a: "<PERSON><PERSON><PERSON> formunu doldurun, ekibimiz sizinle iletişime geçerek uygun saati planlar." },
    { q: "Üyelik dondurma var mı?", a: "<PERSON><PERSON>, sağlık ve seyahat durumlarında belirli sürelerle dondurma yapılabilir." },
    { q: "Sadece kadınlara özel mi?", a: "Eve<PERSON>, stüdyomuz yalnızca kadın üyelerimize hizmet vermektedir." },
  ];

  return (
    <section id="sss" className="section-padding">
      <div className="max-w-3xl mx-auto px-4 md:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-extrabold tracking-tight"><PERSON><PERSON><PERSON></h2>
          <p className="text-muted-foreground mt-2"><PERSON><PERSON> edilenler</p>
        </div>
        <div className="space-y-3">
          {faqs.map((f) => (
            <details key={f.q} className="group rounded-xl border p-5 bg-white/70 dark:bg-white/5">
              <summary className="cursor-pointer list-none select-none font-medium flex justify-between items-center">
                {f.q}
                <span className="text-muted-foreground group-open:rotate-180 transition-transform">⌄</span>
              </summary>
              <div className="mt-3 text-sm text-muted-foreground">{f.a}</div>
            </details>
          ))}
        </div>
      </div>
    </section>
  );
}

