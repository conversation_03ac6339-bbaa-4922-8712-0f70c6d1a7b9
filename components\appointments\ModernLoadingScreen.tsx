"use client";

import { motion } from "framer-motion";

export default function ModernLoadingScreen() {
  return (
    <div className="min-h-[50vh] bg-background flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center space-y-4"
      >
        <div className="w-10 h-10 mx-auto">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-10 h-10 border-2 border-transparent border-t-primary rounded-full"
          />
        </div>
        <div className="space-y-2">
          <h2 className="text-base font-semibold text-foreground">
            Verileriniz Yükleniyor
          </h2>
          <p className="text-muted-foreground">Bir saniye bekleyin...</p>
        </div>
      </motion.div>
    </div>
  );
}
